pull_request_rules:
- name: label-documentation
  description: Automatically apply documentation label
  conditions:
    - or:
      - files~=^[^/]+\.md$
      - files~=^docs/
  actions:
    label:
      add:
        - documentation

- name: label-ci-build
  description: Automatically apply ci/build label
  conditions:
    - or:
      - files~=^\.github/
      - files~=\.buildkite/
      - files~=^cmake/
      - files=CMakeLists.txt
      - files~=^Dockerfile
      - files~=^requirements.*\.txt
      - files=setup.py
  actions:
    label:
      add:
        - ci/build

- name: label-frontend
  description: Automatically apply frontend label
  conditions:
    - files~=^vllm/entrypoints/
  actions:
    label:
      add:
        - frontend

- name: ping author on conflicts and add 'needs-rebase' label
  conditions:
      - conflict
      - -closed
  actions:
    label:
      add:
        - needs-rebase
    comment:
      message: |
       This pull request has merge conflicts that must be resolved before it can be
       merged. Please rebase the PR, @{{author}}.

       https://docs.github.com/en/pull-requests/collaborating-with-pull-requests/working-with-forks/syncing-a-fork

- name: remove 'needs-rebase' label when conflict is resolved
  conditions:
      - -conflict
      - -closed
  actions:
    label:
      remove:
        - needs-rebase
