#!/bin/bash

CUR_DIR=$(dirname "$0")
OUTPUT_DIR=${CUR_DIR}/../utils
MODEL=${MODEL:="meta-llama/Llama-2-7b-hf"}

function profile_attention_bandwidth() {
    local output_path
    output_path="${OUTPUT_DIR}/attention_bandwidth.csv"
    if [[ -f "${output_path}" ]]; then
        rm "${output_path}"
    fi

    python -m adrenaline.profiler.attention_bandwidth_profiler \
        --model ${MODEL} \
        --avg-seq-len 1024 \
        --batch-size 32 \
        --sm-pcts 10 20 30 40 50 60 70 80 90 100 \
        --output-file "${output_path}"
}

profile_attention_bandwidth
