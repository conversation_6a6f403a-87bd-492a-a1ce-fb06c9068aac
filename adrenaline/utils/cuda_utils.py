import contextlib
import torch
import os

from cuda import cuda
from vllm.logger import init_logger

logger = init_logger(__name__)

_cuda_initialized: bool = False


@contextlib.contextmanager
def set_mps_percentage(mps_percentage: int):
    os.environ["CUDA_MPS_ACTIVE_THREAD_PERCENTAGE"] = str(mps_percentage)
    yield
    os.environ.pop("CUDA_MPS_ACTIVE_THREAD_PERCENTAGE")


def init():
    global _cuda_initialized
    if _cuda_initialized == False:
        torch.zeros(1, device="cuda")
        err = cuda.cuInit(0)[0]
        error_handler(err)
        _cuda_initialized = True
    else:
        logger.warning("cuda already initialized")


def _sm_affinity_create(sm_count: int) -> cuda.CUexecAffinityParam:
    affinity = cuda.CUexecAffinityParam()
    affinity.type = cuda.CUexecAffinityType.CU_EXEC_AFFINITY_TYPE_SM_COUNT
    affinity.param.smCount.val = sm_count
    return affinity


def get_sm_count() -> int:
    global _cuda_initialized
    assert _cuda_initialized
    err, affinity = cuda.cuCtxGetExecAffinity(
        cuda.CUexecAffinityType.CU_EXEC_AFFINITY_TYPE_SM_COUNT
    )
    error_handler(err)
    sm_count = affinity.param.smCount.val
    return sm_count


def cu_ctx_create(device: int = 0, sm_count: int = -1):
    global _cuda_initialized
    assert _cuda_initialized
    if sm_count == -1:
        logger.info(f"create default ctx on device {device}")
        err, ctx = cuda.cuCtxCreate(cuda.CUctx_flags.CU_CTX_SCHED_AUTO, device)
        error_handler(err)
        return ctx

    assert (
        os.environ["CUDA_MPS_ENABLE_PER_CTX_DEVICE_MULTIPROCESSOR_PARTITIONING"] == "1"
    )
    affinity = _sm_affinity_create(sm_count)
    affinities = [affinity]
    err, ctx = cuda.cuCtxCreate_v3(
        affinities, len(affinities), cuda.CUctx_flags.CU_CTX_SCHED_AUTO, device
    )
    error_handler(err)
    return ctx


def get_device_name(dev: int, length: int = 32) -> str:
    global _cuda_initialized
    assert _cuda_initialized
    err, bs = cuda.cuDeviceGetName(length, dev)
    error_handler(err)
    dev_name = bs.decode("ascii").rstrip("\x00")
    return dev_name


def error_handler(err):
    assert err == cuda.CUresult.CUDA_SUCCESS, f"{err}: {cuda.cuGetErrorString(err)}"
