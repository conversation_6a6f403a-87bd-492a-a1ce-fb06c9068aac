from vllm.logger import init_logger

from adrenaline.proxy.storage_manager import StorageManager
from adrenaline.proxy.offload_manager.backends.loadaware import (
    is_decode_instance_available,
    is_attn_instance_available,
)

logger = init_logger("vllm.proxy.offload.backends.loadaware")

def offload_with_load_aware_and_decode_first(
    request_id: str,
    m: StorageManager,
    offload_ratio: float,
    prefill_instance_rank: int,
    decode_instance_rank: int,
):
    if is_decode_instance_available(m, request_id, decode_instance_rank):
        location = m.get_decode_location(decode_instance_rank)
    elif is_attn_instance_available(
        m,
        request_id,
        offload_ratio,
        attn_rank=prefill_instance_rank,
        decode_rank=decode_instance_rank,
    ):
        location = m.get_attn_location(prefill_instance_rank)
    else:
        logger.warning("No available decode instance")
        location = m.get_decode_location(decode_instance_rank)
    return location
