import dataclasses
import logging
import statistics

from pathlib import Path
from typing import Optional
from evaluation.plots.nsys.parser import Event, NsysParser

SYS_NAME="AttnSlice"

@dataclasses.dataclass
class CommonColors:
    gradient_blues = ['white', '#85D6E7', '#408EC1', '#07428E', '#BF0000']
    gradient_reds = ['white', '#EAAABB', '#D45555', '#BF0000']
    bak_colors = ['#408EC1', '#D45555', 'C2', 'lightgray', '#F56D42']
    vivid_colors = ['black', '#0000ED', '#BF0000', '#006521', '#6500CC']


def calc_mean_with_process(data: list, filter_pct: Optional[float] = 0.1):
    data = sorted(data)
    if filter_pct is not None:
        length = len(data)
        data = data[int(length * filter_pct) : int(length * (1 - filter_pct))]
    mean_data: float = statistics.mean(data)
    return mean_data


def sample_events(events: list[Event], begin: int = 1, end: int = 4):
    if len(events) >= end:
        return events[begin:end]
    else:
        logging.warning(
            f"invoke sample with not enought events: {len(events)=} < {end}"
        )
        return events


def crop_margins(output_path: str, crop_args: str = "-p 10"):
    import pdfCropMargins
    crop_path = Path(output_path)
    crop_path = crop_path.with_stem(crop_path.stem + "_cropped")
    pdfCropMargins.crop(crop_args.split() + f"{output_path} -o {crop_path}".split())
    return crop_path


@dataclasses.dataclass
class NvtxEventName:
    prefill_forward: str = "llama.py:550(forward)"
    decode_forward: str = "model_runner.py:2684(forward)"
    layer_forward: str = "llama.py:244(forward)"
    # decode_attn: str = "decode.py:568(forward)" # flashinfer v0.1.6
    decode_attn: str = "decode.py:941(forward)" # flashinfer v0.2.1
    send_kv_cache: str = "agent.py:313(send_kv_caches_and_hidden_states)"
    decode_marker: str = "decode.py:568(forward)"


def get_bandwidths_for_prefill(p: NsysParser, trace: NvtxEventName):
    forward_events = p.get_nvtx_events(trace.prefill_forward)
    prefill_start = forward_events[0].cuda_start_time
    fin_event = p.get_nvtx_event(trace.send_kv_cache)
    prefill_end = fin_event.cuda_start_time
    bandwidths, _ = p.get_dram_bw_by_range(prefill_start, prefill_end)
    return bandwidths


def get_compute_powers_for_decode_graph(p: NsysParser, trace: NvtxEventName):
    forward_events = p.get_nvtx_events(trace.decode_forward)
    cuda_graph_events = [e.cuda_graph_event for e in forward_events]
    graph_event = cuda_graph_events[0]
    graph_compute_powers = graph_event.get_comp_power()[0].float()
    return graph_compute_powers


def get_bandwidths_for_decode_graph(p: NsysParser, trace: NvtxEventName):
    forward_events = p.get_nvtx_events(trace.decode_forward)
    cuda_graph_events = [e.cuda_graph_event for e in forward_events]
    graph_event = cuda_graph_events[0]
    graph_bandwidths = graph_event.get_dram_bw()[0].float()
    return graph_bandwidths


def get_decode_ops_events(
    p: NsysParser, trace: NvtxEventName, sample_range: tuple[int, int] = (2, 5)
):
    decode_event = p.get_nvtx_events(trace.decode_marker)[0]
    global_tid = decode_event.global_tid
    layer_events = p.get_nvtx_events(
        trace.layer_forward, global_tid=global_tid
    )
    if sample_range is not None:
        layer_events = layer_events[sample_range[0]:sample_range[1]]
    start = layer_events[0]["start"]
    end = layer_events[-1]["end"]
    linear_events = p.get_nvtx_events("linear", start, end, global_tid)
    qkv_events = [linear_events[i * 4] for i in range(len(layer_events))]
    o_events = [linear_events[i * 4 + 1] for i in range(len(layer_events))]
    ffn0_events = [linear_events[i * 4 + 2] for i in range(len(layer_events))]
    ffn1_events = [linear_events[i * 4 + 3] for i in range(len(layer_events))]
    attn_events = p.get_nvtx_events(
        trace.decode_attn, start, end, global_tid
    )
    ops_events: dict[str, list[Event]] = {
        "qkv_proj": qkv_events,
        "attn": attn_events,
        "o_proj": o_events,
        "ffn0": ffn0_events,
        "ffn1": ffn1_events,
    }
    return ops_events


def get_ops_stats(ops_events: dict[str, list[Event]]):
    latencies = {
        op_name: statistics.mean(
            [e.cuda_end_time - e.cuda_start_time for e in op_events]
        )
        for op_name, op_events in ops_events.items()
    }
    bandwidths = {
        op_name: statistics.mean(
            [e.get_dram_bw()[0].float().max().item() for e in op_events]
        )
        for op_name, op_events in ops_events.items()
    }
    compute_powers = {
        op_name: statistics.mean(
            [e.get_comp_power()[0].float().max().item() for e in op_events]
        )
        for op_name, op_events in ops_events.items()
    }
    return {
        "latency": latencies,
        "bandwidth": bandwidths,
        "compute_power": compute_powers,
    }
