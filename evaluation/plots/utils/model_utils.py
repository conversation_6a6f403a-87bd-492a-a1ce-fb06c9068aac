import dataclasses
import enum


class ModelOperator(enum.StrEnum):
    QKV_PROJ = "qkv_proj"
    ATTN = "attn"
    O_PROJ = "o_proj"
    FFN0 = "ffn0"
    FFN1 = "ffn1"


@dataclasses.dataclass
class ModelComputeFLOPS:
    hidden_size: int
    head_size: int
    num_heads: int
    num_kv_heads: int
    intermediate_size: int
    num_layers: int

    @property
    def qkv_proj(self):
        return (
            self.hidden_size * self.head_size * (self.num_heads + self.num_kv_heads * 2)
        )

    @property
    def o_proj(self):
        return self.hidden_size * self.hidden_size

    @property
    def ffn0(self):
        return self.intermediate_size * self.hidden_size * 2

    @property
    def ffn1(self):
        return self.intermediate_size * self.hidden_size

    @property
    def model(self):
        return self.num_layers * (
            self.qkv_proj + self.o_proj + self.ffn0 + self.ffn1
        )


LLAMA2_7B = ModelComputeFLOPS(
    hidden_size=4096,
    head_size=128,
    num_heads=32,
    num_kv_heads=32,
    intermediate_size=11008,
    num_layers=32,
)

LLAMA2_13B = ModelComputeFLOPS(
    hidden_size=5120,
    head_size=128,
    num_heads=40,
    num_kv_heads=40,
    intermediate_size=13824,
    num_layers=40,
)

def to_model_compute_flops(model_name: str):
    if model_name == "Llama-2-7b-hf":
        return LLAMA2_7B
    elif model_name == "Llama-2-13b-hf":
        return LLAMA2_13B
    else:
        raise ValueError(f"Unknown model_name {model_name}")


def get_model_compute_flops(model: ModelComputeFLOPS, batch: int):
    return model.model * batch * 2


def get_gemm_compute_flops(model: ModelComputeFLOPS, op_name: str, batch: int):
    if op_name == ModelOperator.QKV_PROJ:
        return model.qkv_proj * batch * 2
    elif op_name == ModelOperator.O_PROJ:
        return model.o_proj * batch * 2
    elif op_name == ModelOperator.FFN0:
        return model.ffn0 * batch * 2
    elif op_name == ModelOperator.FFN1:
        return model.ffn1 * batch * 2
    else:
        raise ValueError(f"Unknown op_name {op_name}")


def get_attn_compute_flops(model: ModelComputeFLOPS, batch: int, seqlens: int):
    return 4 * batch * model.hidden_size * seqlens
