import argparse
import glob
import torch
import statistics
import numpy

from dataclasses import dataclass
from matplotlib import pyplot
from matplotlib.ticker import MultipleLocator
from evaluation.plots.nsys.parser import NsysParser
from evaluation.plots.nsys.utils import A100_80GB_META_DATA
from evaluation.plots.utils.plot_utils import (
    NvtxEventName,
    get_bandwidths_for_prefill,
    get_decode_ops_events,
    get_ops_stats,
    CommonColors,
    crop_margins,
)
from evaluation.plots.utils.model_utils import (
    LLAMA2_7B,
    ModelOperator,
    get_gemm_compute_flops,
    get_attn_compute_flops,
)

scheme_to_label = {
    'vllm': 'vLLM',
    '0.2': 'Offload 20%',
    '0.4': 'Offload 40%',
    '0.6': 'Offload 60%',
    '0.8': 'Offload 80%',
}

@dataclass
class PlotConfig:
    # colors = ['#0000ED', '#EAAABB', '#D45555', '#BF0000']
    colors = [CommonColors.gradient_blues[2]] + CommonColors.gradient_reds[1:]
    marker_style = ['o', 's', '^', 'v', 'D', '<', '>', 's', 'x', '*']
    bar_width = 0.13
    bar_padding = 1.4

def get_prefill_bandwidth_for_disagg(profile_dir: str, num_locals: int, trace: NvtxEventName):
    profile_path = glob.glob(f"{profile_dir}/disagg_prefill_num_locals_{num_locals}*.sqlite")[0]
    parser = NsysParser(profile_path)
    bandwidths = get_bandwidths_for_prefill(parser, trace)
    avg_bandwidth = statistics.fmean(bandwidths)
    max_bandwidth = bandwidths.max()
    return avg_bandwidth, max_bandwidth


def get_prefill_bandwidths_breakdown_for_offload(profile_dir: str, offload_ratio: float, num_locals: int, trace: NvtxEventName, threshold: float = 70):
    profile_path = glob.glob(
        f"{profile_dir}/offload_prefill_ratio_{offload_ratio}_num_locals_{num_locals}*.sqlite"
    )[0]
    parser = NsysParser(profile_path)
    bandwidths = get_bandwidths_for_prefill(parser, trace)
    bandwidth_stat = get_prefill_bandwidth_stat_for_offload(bandwidths)
    return bandwidth_stat


def get_prefill_bandwidth_stat_for_offload(
    bandwidths: torch.Tensor, bw_threshold: float = 25, time_threshold: int = 15
):
    large_bandwidths: list[float] = []
    offload_bandwidths: list[list[float]] = []
    non_offload_bandwidths: list[float] = []
    for bw_tensor in bandwidths:
        bw: float = bw_tensor.item()
        if bw > bw_threshold:
            large_bandwidths.append(bw)
        else:
            if len(large_bandwidths) > time_threshold:
                offload_bandwidths.append(large_bandwidths)
            else:
                non_offload_bandwidths.extend(large_bandwidths)
            large_bandwidths = []
            non_offload_bandwidths.append(bw)

    if len(large_bandwidths) > time_threshold:
        offload_bandwidths.append(large_bandwidths)
    else:
        non_offload_bandwidths.extend(large_bandwidths)
    print(f"execute with {len(offload_bandwidths)} times offloaded attn")
    offload_bandwidths = sum(offload_bandwidths, [])

    latencies = {
        "without_offload": len(non_offload_bandwidths) * 5.0, # us
        "with_offload": len(offload_bandwidths) * 5.0, # us
    }
    bandwidth_pct = {
        "without_offload": statistics.fmean(non_offload_bandwidths),
        "with_offload": statistics.fmean(offload_bandwidths),
    }
    print(f"{latencies=} (us)")
    return {
        "latency": latencies,
        "bandwidth": bandwidth_pct,
    }


def get_decode_ops_stats_for_disagg(profile_dir: str, num_locals: int, trace: NvtxEventName):
    profile_path = glob.glob(f"{profile_dir}/disagg_decode_num_locals_{num_locals}*.sqlite")[0]
    parser = NsysParser(profile_path)
    ops_events = get_decode_ops_events(parser, trace)
    ops_stats = get_ops_stats(ops_events)
    return ops_stats


def get_decode_ops_stats_for_offload(profile_dir: str, offload_ratio: float, num_locals: int, offload_trace: NvtxEventName):
    profile_path = glob.glob(f"{profile_dir}/offload_decode_ratio_{offload_ratio}_num_locals_{num_locals}*.sqlite")[0]
    parser = NsysParser(profile_path)
    ops_events = get_decode_ops_events(parser, offload_trace)
    ops_stats = get_ops_stats(ops_events)
    return ops_stats


def process_prefill_bandwidth_breakdown(
    n_groups: int,
    disagg_bandwidth_pct: float,
    offload_bandwidth_pcts: dict[float, dict[str, float]],
    offload_ratios: list[float],
):
    data = {'vllm': [disagg_bandwidth_pct / 100 * A100_80GB_META_DATA.MEMORY_BANDWIDTH] * n_groups}
    schemes = ['vllm']
    for ratio in offload_ratios:
        ratio_name = str(ratio)
        schemes.append(ratio_name)

        bw_ratio = [
            offload_bandwidth_pcts[ratio]['without_offload'],
            offload_bandwidth_pcts[ratio]['with_offload'],
        ]
        bws = [x / 100 * A100_80GB_META_DATA.MEMORY_BANDWIDTH
               for x in bw_ratio]
        data[ratio_name] = bws

    return schemes, data

def get_weighted_ffn(ffn0, ffn1, ffn0_time, ffn1_time):
    return (ffn0 * ffn0_time + ffn1 * ffn1_time) / (
        ffn0_time + ffn1_time
    )

def process_decode_compute_power_breakdown_v1(
    disagg_decode_ops_stat: dict[str, dict[str, float]],
    offload_decode_ops_stats: dict[float, dict[str, dict[str, float]]],
    offload_ratios: list[float],
):
    metric = 'compute_power'

    vllm_breakdown = [
        disagg_decode_ops_stat[metric]['qkv_proj'],
        disagg_decode_ops_stat[metric]['attn'],
        disagg_decode_ops_stat[metric]['o_proj'],
        get_weighted_ffn(
            disagg_decode_ops_stat[metric]['ffn0'],
            disagg_decode_ops_stat[metric]['ffn1'],
            disagg_decode_ops_stat['latency']['ffn0'],
            disagg_decode_ops_stat['latency']['ffn1']
        )
    ]
    print(f'{vllm_breakdown=}')

    data = {'vllm': [ratio / 100 * A100_80GB_META_DATA.PEAK_FP16_TFLOPS 
                    for ratio in vllm_breakdown]}
    schemes = ['vllm']

    for ratio in offload_ratios:
        ratio_name = str(ratio)
        schemes.append(ratio_name)

        comp_ratio = [
            offload_decode_ops_stats[ratio][metric]['qkv_proj'],
            offload_decode_ops_stats[ratio][metric]['attn'],
            offload_decode_ops_stats[ratio][metric]['o_proj'],
            get_weighted_ffn(
                offload_decode_ops_stats[ratio][metric]['ffn0'],
                offload_decode_ops_stats[ratio][metric]['ffn1'],
                offload_decode_ops_stats[ratio]['latency']['ffn0'],
                offload_decode_ops_stats[ratio]['latency']['ffn1']
            )
        ]
        print(f'{ratio_name=}, {comp_ratio=}')
        data[ratio_name] = [ratio / 100 * A100_80GB_META_DATA.PEAK_FP16_TFLOPS 
                            for ratio in comp_ratio]

    return schemes, data

def process_decode_compute_power_breakdown_v2(
    disagg_decode_ops_stat: dict[str, dict[str, float]],
    offload_decode_ops_stats: dict[float, dict[str, dict[str, float]]],
    offload_ratios: list[float],
):
    ops_name = ['qkv_proj', 'attn', 'o_proj', 'ffn0', 'ffn1']
    num_locals = 55
    seqlens = 1024
    batch_sizes = [num_locals] + [
        int(num_locals * (1 + ratio)) for ratio in offload_ratios
    ]
    configs_ops_compute_flops = {
        op_name: [
            get_gemm_compute_flops(LLAMA2_7B, op_name, batch) for batch in batch_sizes
        ]
        for op_name in ops_name
        if op_name != ModelOperator.ATTN
    }
    configs_ops_compute_flops[ModelOperator.ATTN] = [
        get_attn_compute_flops(LLAMA2_7B, num_locals, seqlens)
        for _ in range(len(batch_sizes))
    ]
    print(f'{configs_ops_compute_flops=}')

    configs_ops_stat = [disagg_decode_ops_stat] + [
        offload_decode_ops_stats[ratio] for ratio in offload_ratios
    ]

    schemes = ['vllm'] + [
        ratio for ratio in offload_ratios
    ]

    data = {
        str(scheme): [
            # convert to TFLOPS / s
            configs_ops_compute_flops[op_name][i] / configs_ops_stat[i]['latency'][op_name] / 1000
            for op_name in ops_name[:3]
        ]
        for i, scheme in enumerate(schemes)
    }

    # combine ffn0 and ffn1 to ffn
    for i, scheme in enumerate(schemes):
        ffn_flops = (
            configs_ops_compute_flops['ffn0'][i] + configs_ops_compute_flops['ffn1'][i]
        ) / (
            configs_ops_stat[i]['latency']['ffn0'] + configs_ops_stat[i]['latency']['ffn1']
        ) / 1000
        data[str(scheme)].append(ffn_flops)

    schemes = [str(x) for x in schemes]
    print(f'{schemes=}')
    print(f'{data=}')
    return schemes, data

def plot_prefill_bandwidth_breakdown_new(
    n_groups: int,
    schemes: list[str],
    data: dict[str, list[float]],
    output_path: str,
):
    fig, ax1 = pyplot.subplots(figsize=(1.6, 2))
    # ax2 = ax1.twinx()

    x_ticks = ['Attn off', 'Attn on']
    assert n_groups == len(x_ticks)

    x = numpy.arange(n_groups) # the label locations
    width = PlotConfig.bar_width  # the width of the bars
    offsets = numpy.arange(len(schemes)) * width - width * (len(schemes) - 1) / 2
    offsets = offsets * PlotConfig.bar_padding # add spaces for bars in a group

    group_labels = [scheme_to_label[s] for s in schemes]

    for i in range(len(schemes)):
        scheme = schemes[i]
        tag = scheme_to_label[scheme]
        print(scheme, tag)
        target_value = data[scheme]
        # ax1.bar(x + offsets[i], target_value, width, label=tag,
        #         color=PlotConfig.colors[i])
        ax1.bar(x + offsets[i], target_value, width, label=tag,
                color=PlotConfig.colors[i], edgecolor='black', linewidth=1)

    pyplot.xticks(x, x_ticks)

    # ax1.set_xlabel('Prompt length')
    ax1.set_ylabel('Mem BW (GB/s)')

    # ax2.set_ylabel('Execute Time Ratio (%)', color='red')
    # ax2.yaxis.label.set_color('red')

    ax1.set_ylim([0, 2000])
    # ax2.set_ylim([0, 100])

    # ax1.legend(group_labels, ncol=4, loc='upper center', fontsize=9)
    # ax1.legend(group_labels, frameon=False)
    pyplot.grid(axis='y', color='lightgray', linestyle='--')
    ax1.set_axisbelow(True)
    # ax2.set_axisbelow(True)

    pyplot.savefig(output_path, bbox_inches='tight')


def plot_decode_ops_compute_power_new(
    n_groups: int,
    schemes: list[str],
    data: dict[str, list[float]],
    output_path: str,
):
    print(f'>>>>>> {data=}')
    fig, ax1 = pyplot.subplots(figsize=(3, 2))
    # ax2 = ax1.twinx()

    x_ticks = ['qkv_proj', 'attn', 'o_proj', 'ffn']
    assert n_groups == len(x_ticks)

    x = numpy.arange(n_groups) # the label locations
    width = PlotConfig.bar_width  # the width of the bars
    offsets = numpy.arange(len(schemes)) * width - width * (len(schemes) - 1) / 2
    offsets = offsets * PlotConfig.bar_padding # add spaces for bars in a group

    group_labels = [scheme_to_label[s] for s in schemes]

    plot_with_legend = 'legend' in output_path 

    for i in range(len(schemes)):
        scheme = schemes[i]
        tag = scheme_to_label[scheme]
        print(scheme, tag)
        target_value = data[scheme]
        # ax1.bar(x + offsets[i], target_value, width, label=tag,
        #         color=PlotConfig.colors[i])
        ax1.bar(x + offsets[i], target_value, width, label=tag,
                color=PlotConfig.colors[i], edgecolor='black', linewidth=1)

    pyplot.xticks(x, x_ticks)

    # ax1.set_xlabel('Prompt length')
    ax1.set_ylabel('Compute Power (TFLOPS)')

    # ax2.set_ylabel('Execute Time Ratio (%)', color='red')
    # ax2.yaxis.label.set_color('red')

    ax1.set_ylim([0, 150])
    # ax2.set_ylim([0, 100])

    if plot_with_legend:
        fig.legend(group_labels, ncol=4, loc='upper center', frameon=False)
        fig.subplots_adjust(top=0.7)
    # ax1.legend(group_labels, frameon=False)

    ax1.yaxis.set_major_locator(MultipleLocator(50))

    pyplot.grid(axis='y', color='lightgray', linestyle='--')
    ax1.set_axisbelow(True)
    # ax2.set_axisbelow(True)

    pyplot.savefig(output_path, bbox_inches='tight')

def plot_prefill_bandwidth_breakdown(
    disagg_bandwidth_pct: float,
    offload_bandwidth_pcts: dict[float, dict[str, float]],
    offload_ratios: list[float],
    output_path: str,
):
    groups = [f"offload_ratio={ratio}" for ratio in offload_ratios]
    parts_name = list(offload_bandwidth_pcts[offload_ratios[0]].keys())
    total_width = 0.8
    spacing_factor = 1
    bar_width = total_width / (len(parts_name) + spacing_factor)
    configs_parts_barpos = {
        part_name: [grp_idx + bar_idx * bar_width for grp_idx in range(len(groups))]
        for bar_idx, part_name in enumerate(parts_name)
    }
    configs_parts_bandwidth = {
        part: [
            offload_bandwidth_pcts[ratio][part] / 100 * A100_80GB_META_DATA.MEMORY_BANDWIDTH
            for ratio in offload_ratios
        ]
        for part in parts_name
    }
    fig, ax = pyplot.subplots()
    for part in parts_name:
        ax.bar(
            configs_parts_barpos[part],
            configs_parts_bandwidth[part],
            width=bar_width,
            label=part,
        )
    ax.hlines(
        disagg_bandwidth_pct / 100 * A100_80GB_META_DATA.MEMORY_BANDWIDTH,
        configs_parts_barpos[parts_name[0]][0] - bar_width / 2,
        configs_parts_barpos[parts_name[-1]][-1] + bar_width / 2,
        colors="r",
        linestyles="dashed",
        label="disagg",
    )
    ax.set_xticks(
        [i + (bar_width * (len(parts_name) - 1)) / 2 for i in range(len(groups))]
    )
    ax.set_xticklabels(groups)
    ax.set_ylabel("Bandwidth / (GB/s)")
    ax.legend()
    fig.savefig(output_path)


# def plot_decode_ops_compute_power_by_nsys_capture(
#     disagg_decode_ops_stat: dict[str, dict[str, float]],
#     offload_decode_ops_stats: dict[float, dict[str, dict[str, float]]],
#     offload_ratios: list[float],
#     output_path: str,
# ):
#     groups = ["disagg"] + [f"offload_ratio={ratio}" for ratio in offload_ratios]
#     ops_name = list(disagg_decode_ops_stat["compute_power"].keys())
#     total_width = 0.8
#     spacing_factor = 1
#     bar_width = total_width / (len(ops_name) + spacing_factor)
#     configs_ops_barpos = {
#         op_name: [grp_idx + bar_idx * bar_width for grp_idx in range(len(groups))]
#         for bar_idx, op_name in enumerate(ops_name)
#     }
#     configs_ops_stat = [disagg_decode_ops_stat] + [
#         offload_decode_ops_stats[ratio] for ratio in offload_ratios
#     ]
#     configs_ops_compute_power = {
#         op_name: [
#             stat["compute_power"][op_name] / 100 * A100_80GB_META_DATA.PEAK_FP16_TFLOPS
#             for stat in configs_ops_stat
#         ]
#         for op_name in ops_name
#     }
#     fig, ax = pyplot.subplots()
#     for op_name in ops_name:
#         ax.bar(configs_ops_barpos[op_name], configs_ops_compute_power[op_name], width=bar_width, label=op_name)
#     ax.set_xticks(
#         [i + (bar_width * (len(ops_name) - 1)) / 2 for i in range(len(groups))]
#     )
#     ax.set_xticklabels(groups)
#     ax.set_ylabel("Compute Power / TFLOPS")
#     ax.legend()
#     fig.savefig(output_path)


def plot_decode_ops_compute_power(
    disagg_decode_ops_stat: dict[str, dict[str, float]],
    offload_decode_ops_stats: dict[float, dict[str, dict[str, float]]],
    offload_ratios: list[float],
    output_path: str,
):
    groups = ["disagg"] + [f"offload_ratio={ratio}" for ratio in offload_ratios]
    ops_name = list(disagg_decode_ops_stat["compute_power"].keys())
    total_width = 0.8
    spacing_factor = 1
    bar_width = total_width / (len(ops_name) + spacing_factor)
    configs_ops_barpos = {
        op_name: [grp_idx + bar_idx * bar_width for grp_idx in range(len(groups))]
        for bar_idx, op_name in enumerate(ops_name)
    }
    configs_ops_stat = [disagg_decode_ops_stat] + [
        offload_decode_ops_stats[ratio] for ratio in offload_ratios
    ]
    num_locals = 55
    seqlens = 1024
    batch_sizes = [num_locals] + [
        int(num_locals * (1 + ratio)) for ratio in offload_ratios
    ]
    configs_ops_compute_flops = {
        op_name: [
            get_gemm_compute_flops(LLAMA2_7B, op_name, batch) for batch in batch_sizes
        ]
        for op_name in ops_name
        if op_name != ModelOperator.ATTN
    }
    configs_ops_compute_flops[ModelOperator.ATTN] = [
        get_attn_compute_flops(LLAMA2_7B, num_locals, seqlens)
        for _ in range(len(batch_sizes))
    ]
    configs_ops_compute_power = {
        op_name: [
            # convert to TFLOPS / s
            configs_ops_compute_flops[op_name][i] / stat["latency"][op_name] / 1000
            for i, stat in enumerate(configs_ops_stat)
        ]
        for op_name in ops_name
    }
    print(f'{configs_ops_compute_power=}')
    fig, ax = pyplot.subplots()
    for op_name in ops_name:
        ax.bar(configs_ops_barpos[op_name], configs_ops_compute_power[op_name], width=bar_width, label=op_name)
    ax.set_xticks(
        [i + (bar_width * (len(ops_name) - 1)) / 2 for i in range(len(groups))]
    )
    ax.set_xticklabels(groups)
    ax.set_ylabel("Compute Power / TFLOPS")
    ax.legend()
    fig.savefig(output_path)


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--profile-dir", type=str, default="evaluation/results/resource_utilization_breakdown"
    )
    parser.add_argument("--offload-ratios", nargs="+", type=float, default=[0.4, 0.6, 0.8])
    parser.add_argument("--num-locals", type=int, default=55)
    parser.add_argument("--output-dir", type=str, default="evaluation/plots/resource_utilization_breakdown")
    parser.add_argument("--use-existed-data", action="store_true")
    parser.add_argument("--crop-margin", action="store_true")
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    disagg_trace = NvtxEventName(
        prefill_forward="llama.py:550(forward)",
        decode_forward="model_runner.py:2684(forward)",
        layer_forward="llama.py:244(forward)",
        # decode_attn="decode.py:568(forward)", # flashinfer v0.1.6
        # decode_attn="decode.py:941(forward)", # flashinfer v0.2.1
        decode_attn="decode.py:942(forward)",  # flashinfer v0.2.3
        send_kv_cache="agent.py:313(send_kv_caches_and_hidden_states)",
        # decode_marker="decode.py:568(forward)", # flashinfer v0.1.6
        # decode_marker="decode.py:941(forward)", # flashinfer v0.2.1
        decode_marker="decode.py:942(forward)",  # flashinfer v0.2.3
    )
    offload_trace = NvtxEventName(
        prefill_forward="llama.py:245(forward)",
        decode_forward="model_runner.py:110(forward)",
        layer_forward="llama.py:160(forward)",
        # decode_attn="decode.py:568(forward)", # flashinfer v0.1.6
        # decode_attn = "decode.py:941(forward)", # flashinfer v0.2.1
        decode_attn="decode.py:942(forward)",  # flashinfer v0.2.3
        send_kv_cache="agent.py:313(send_kv_caches_and_hidden_states)",
        decode_marker="llama.py:72(forward_with_offload)"
    )

    offload_ratios: list[float] = args.offload_ratios

    if args.use_existed_data:
        disagg_bandwidth = 22.360983346550356
        offload_bandwidths_breakdown = {0.2: {'without_offload': 15.017880196230312, 'with_offload': 73.37194775695627}, 0.4: {'without_offload': 14.999361837906829, 'with_offload': 76.28694362017804}, 0.6: {'without_offload': 15.071636163694919, 'with_offload': 77.86192893401015}}

        disagg_decode_ops_stats: dict[str, dict[str, float]] = {'latency': {'qkv_proj': 76340.33333333333, 'attn': 406781, 'o_proj': 28149.333333333332, 'ffn0': 114996.33333333333, 'ffn1': 68191.33333333333}, 'bandwidth': {'qkv_proj': 80.33333333333333, 'attn': 98.0, 'o_proj': 78.33333333333333, 'ffn0': 90.0, 'ffn1': 89.33333333333333}, 'compute_power': {'qkv_proj': 41.0, 'attn': 19.666666666666668, 'o_proj': 40.0, 'ffn0': 46.333333333333336, 'ffn1': 45.333333333333336}}
        offload_decode_ops_stats = {0.2: {'latency': {'qkv_proj': 67893, 'attn': 408658.3333333333, 'o_proj': 27488, 'ffn0': 117823, 'ffn1': 67797}, 'bandwidth': {'qkv_proj': 89.33333333333333, 'attn': 99.0, 'o_proj': 82.66666666666667, 'ffn0': 94.66666666666667, 'ffn1': 91.0}, 'compute_power': {'qkv_proj': 45.333333333333336, 'attn': 1.0, 'o_proj': 42.333333333333336, 'ffn0': 49.0, 'ffn1': 47.666666666666664}}, 0.4: {'latency': {'qkv_proj': 66037, 'attn': 400840, 'o_proj': 29375.666666666668, 'ffn0': 115881.66666666667, 'ffn1': 70175.33333333333}, 'bandwidth': {'qkv_proj': 93.66666666666667, 'attn': 98.66666666666667, 'o_proj': 74.66666666666667, 'ffn0': 91.33333333333333, 'ffn1': 91.33333333333333}, 'compute_power': {'qkv_proj': 48.333333333333336, 'attn': 1.0, 'o_proj': 38.0, 'ffn0': 48.666666666666664, 'ffn1': 47.333333333333336}}, 0.6: {'latency': {'qkv_proj': 66730, 'attn': 400295.6666666667, 'o_proj': 28853.333333333332, 'ffn0': 117652.33333333333, 'ffn1': 70805}, 'bandwidth': {'qkv_proj': 93.66666666666667, 'attn': 98.66666666666667, 'o_proj': 80.33333333333333, 'ffn0': 91.33333333333333, 'ffn1': 90.33333333333333}, 'compute_power': {'qkv_proj': 47.666666666666664, 'attn': 1.0, 'o_proj': 40.666666666666664, 'ffn0': 49.0, 'ffn1': 46.666666666666664}}}
    else:
        disagg_bandwidth, threshold = get_prefill_bandwidth_for_disagg(
            args.profile_dir, args.num_locals, disagg_trace
        )
        offload_bandwidths_breakdown = {
            offload_ratio: get_prefill_bandwidths_breakdown_for_offload(
                args.profile_dir, offload_ratio, args.num_locals, offload_trace, threshold
            )["bandwidth"]
            for offload_ratio in offload_ratios
        }
        print(f'{disagg_bandwidth=}')
        print(f'{offload_bandwidths_breakdown=}')
        print(f'{offload_ratios=}')

        disagg_decode_ops_stats = get_decode_ops_stats_for_disagg(
            args.profile_dir, args.num_locals, disagg_trace
        )
        offload_decode_ops_stats = {
            offload_ratio: get_decode_ops_stats_for_offload(
                args.profile_dir, offload_ratio, args.num_locals, offload_trace
            )
            for offload_ratio in offload_ratios
        }
        print(f'{disagg_decode_ops_stats=}')
        print(f'{offload_decode_ops_stats=}')
        print(f'{offload_ratios=}')

    schemes, data = process_prefill_bandwidth_breakdown(
        n_groups=2,
        disagg_bandwidth_pct=disagg_bandwidth,
        offload_bandwidth_pcts=offload_bandwidths_breakdown,
        offload_ratios=offload_ratios
    )
    plot_prefill_bandwidth_breakdown_new(
        2, schemes, data,
        f"{args.output_dir}/prefill_bandwidth_breakdown.pdf",
    )

    schemes, data = process_decode_compute_power_breakdown_v2(
        disagg_decode_ops_stat=disagg_decode_ops_stats,
        offload_decode_ops_stats=offload_decode_ops_stats,
        offload_ratios=offload_ratios
    )
    # plot with lengeds (to extract the legend for our paper)
    plot_decode_ops_compute_power_new(
        4, schemes, data,
        f"{args.output_dir}/decode_ops_compute_power.pdf",
    )
    plot_decode_ops_compute_power_new(
        4, schemes, data,
        f"{args.output_dir}/decode_ops_compute_power_with_legend.pdf",
    )
    if args.crop_margin:
        crop_path = crop_margins(f"{args.output_dir}/prefill_bandwidth_breakdown.pdf")
        print(f"Cropped to {crop_path} successfully.")
        crop_path = crop_margins(f"{args.output_dir}/decode_ops_compute_power.pdf")
        print(f"Cropped to {crop_path} successfully.")
        crop_path = crop_margins(f"{args.output_dir}/decode_ops_compute_power_with_legend.pdf", "-p4 10 -3300 10 10")
        print(f"Cropped to {crop_path} successfully.")
