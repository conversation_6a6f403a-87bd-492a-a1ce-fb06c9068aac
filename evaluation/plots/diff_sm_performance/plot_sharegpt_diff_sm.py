import argparse
import matplotlib

from matplotlib import pyplot
from matplotlib.ticker import MultipleLocator


from evaluation.plots.diff_sm_performance.utils import PlotConfig
from evaluation.plots.dataset_performance.utils import (
    get_ttft_for_config,
    get_tpot_for_config,
    get_thpt_for_config,
    get_p99_tpot_for_config,
)
from evaluation.plots.utils.plot_utils import SYS_NAME, crop_margins

scheme_to_label = {
    "0.1": "sm 10%",
    "0.3": f"sm 30% ({SYS_NAME})",
    "0.5": "sm 50%",
}

matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 10
matplotlib.rcParams['axes.titlesize'] = 10
matplotlib.rcParams['axes.labelsize'] = 10
matplotlib.rcParams['xtick.labelsize'] = 10
matplotlib.rcParams['ytick.labelsize'] = 10
matplotlib.rcParams['legend.fontsize'] = 10


def process_ttft(
    sm_partitions: list[float],
    offload_ttfts: dict[float, list[float]]
):
    schemes = [str(sm_par) for sm_par in sm_partitions]
    data = {
        scheme: [t / 1000 for t in offload_ttfts[sm_par]]
        for sm_par, scheme in zip(sm_partitions, schemes)
    }
    return schemes, data

def process_tpot(
    offload_ratios: list[float],
    offload_tpots: dict[float, list[float]]
):
    schemes = []
    data = {}
    for ratio in offload_ratios:
        scheme = str(ratio)
        schemes.append(scheme)
        data[scheme] = offload_tpots[ratio]
    return schemes, data

def process_p99_tpot(
    offload_ratios: list[float],
    offload_tpots: dict[float, list[float]]
):
    return process_tpot(offload_ratios, offload_tpots)

def process_stable_thpt(
    offload_ratios: list[float],
    offload_thpts: dict[float, list[float]]
):
    schemes = []
    data = {}
    for ratio in offload_ratios:
        scheme = str(ratio)
        schemes.append(scheme)
        data[scheme] = [
            x * 1000 for x in offload_thpts[ratio]
        ]
    return schemes, data

def plot_ttft(
    qps_list: list[float],
    schemes: list[str],
    data: dict[str, list[float]],
    output_path: str,
):
    print(f'{schemes=}')
    print(f'{data=}')
    # fig, ax = pyplot.subplots(figsize=(3.2, 2)) # 1/3 column
    fig, ax = pyplot.subplots(figsize=PlotConfig.figsize) # 1/4 column

    for i, scheme in enumerate(schemes):
        label = scheme_to_label[scheme]
        ttfts = data[scheme]
        ax.plot(qps_list, ttfts, marker=PlotConfig.marker_style[i],
                color=PlotConfig.colors[i], markersize=PlotConfig.markersize,
                markerfacecolor='none', markeredgewidth=PlotConfig.markeredgewidth,
                label=label)

    # ax.xaxis.set_major_locator(MultipleLocator(1))
    ax.yaxis.set_major_locator(MultipleLocator(2))

    pyplot.grid(axis='y', color='lightgray', linestyle='--', linewidth=PlotConfig.grid_linewidth)
    pyplot.grid(axis='x', color='lightgray', linestyle='--', linewidth=PlotConfig.grid_linewidth)

    pyplot.ylim([0, 4])

    pyplot.xlabel("Req rate (req/s)")
    pyplot.ylabel("Mean TTFT (s)")
    pyplot.tight_layout()
    if PlotConfig.with_legend:
        pyplot.legend(frameon=False)
    # fig.legend(loc=(0.50, 0.56), frameon=False)  # 设置背景
    pyplot.savefig(output_path)

def plot_ttft_for_legend(
    qps_list: list[float],
    schemes: list[str],
    data: dict[str, list[float]],
    output_path: str,
):
    print(f'{schemes=}')
    print(f'{data=}')
    # fig, ax = pyplot.subplots(figsize=(3.2, 2)) # 1/3 column
    fig, ax = pyplot.subplots(figsize=PlotConfig.figsize) # 1/4 column

    group_labels = []

    for i, scheme in enumerate(schemes):
        label = scheme_to_label[scheme]
        group_labels.append(label)
        ttfts = data[scheme]
        ax.plot(qps_list, ttfts, marker=PlotConfig.marker_style[i],
                color=PlotConfig.colors[i], markersize=PlotConfig.markersize,
                markerfacecolor='none', markeredgewidth=PlotConfig.markeredgewidth,
                label=label)

    pyplot.grid(axis='y', color='lightgray', linestyle='--', linewidth=PlotConfig.grid_linewidth)
    pyplot.grid(axis='x', color='lightgray', linestyle='--', linewidth=PlotConfig.grid_linewidth)

    pyplot.ylim([0, 4])

    ax.set_xlabel("Req rate (req/s)")
    ax.set_ylabel("Mean TTFT (s)")
    fig.legend(group_labels, ncol=4, loc='upper center', frameon=False)
    fig.subplots_adjust(top=0.7)
    pyplot.savefig(output_path, bbox_inches='tight')


def plot_tpot(
    qps_list: list[float],
    schemes: list[str],
    data: dict[str, list[float]],
    output_path: str,
):
    print(f'{schemes=}')
    print(f'{data=}')
    # fig, ax = pyplot.subplots(figsize=(3.2, 2)) # 1/3 column
    fig, ax = pyplot.subplots(figsize=PlotConfig.figsize) # 1/4 column

    for i, scheme in enumerate(schemes):
        label = scheme_to_label[scheme]
        ttfts = data[scheme]
        ax.plot(qps_list, ttfts, marker=PlotConfig.marker_style[i],
                color=PlotConfig.colors[i], markersize=PlotConfig.markersize,
                markerfacecolor='none', markeredgewidth=PlotConfig.markeredgewidth,
                label=label)

    # ax.xaxis.set_major_locator(MultipleLocator(1))
    # ax.yaxis.set_major_locator(MultipleLocator(10))

    pyplot.grid(axis='y', color='lightgray', linestyle='--', linewidth=PlotConfig.grid_linewidth)
    pyplot.grid(axis='x', color='lightgray', linestyle='--', linewidth=PlotConfig.grid_linewidth)

    pyplot.ylim([20, 80])

    ax.set_xlabel("Req rate (req/s)")
    ax.set_ylabel("Mean TPOT (ms)")
    pyplot.tight_layout()
    if PlotConfig.with_legend:
        fig.legend(loc=(0.52, 0.3), frameon=False, markerfirst=False)
    pyplot.savefig(output_path)


def plot_p99_tpot(
    qps_list: list[float],
    schemes: list[str],
    data: dict[str, list[float]],
    output_path: str,
):
    print(f'{schemes=}')
    print(f'{data=}')
    fig, ax = pyplot.subplots(figsize=PlotConfig.figsize) # 1/4 column

    for i, scheme in enumerate(schemes):
        label = scheme_to_label[scheme]
        ttfts = data[scheme]
        ax.plot(qps_list, ttfts, marker=PlotConfig.marker_style[i],
                color=PlotConfig.colors[i], markersize=PlotConfig.markersize,
                markerfacecolor='none', markeredgewidth=PlotConfig.markeredgewidth,
                label=label)


    ax.xaxis.set_major_locator(MultipleLocator(1))
    ax.yaxis.set_major_locator(MultipleLocator(20))

    pyplot.grid(axis='y', color='lightgray', linestyle='--', linewidth=PlotConfig.grid_linewidth)
    pyplot.grid(axis='x', color='lightgray', linestyle='--', linewidth=PlotConfig.grid_linewidth)

    # pyplot.ylim([0, 100])

    ax.set_xlabel("Req rate (req/s)")
    ax.set_ylabel("P99 TPOT (ms)")
    pyplot.tight_layout()
    if PlotConfig.with_legend:
        fig.legend(loc=(0.52, 0.3), frameon=False, markerfirst=False)
    pyplot.savefig(output_path)


def plot_stable_thpt(
    qps_list: list[float],
    schemes: list[str],
    data: dict[str, list[float]],
    output_path: str,
):
    print(f'{schemes=}')
    print(f'{data=}')
    # fig, ax = pyplot.subplots(figsize=(3.2, 2)) # 1/3 column
    fig, ax = pyplot.subplots(figsize=PlotConfig.figsize) # 1/4 column

    for i, scheme in enumerate(schemes):
        label = scheme_to_label[scheme]
        ttfts = data[scheme]
        ax.plot(qps_list, ttfts, marker=PlotConfig.marker_style[i],
                color=PlotConfig.colors[i], markersize=PlotConfig.markersize,
                markerfacecolor='none', markeredgewidth=PlotConfig.markeredgewidth,
                label=label)

    # ax.xaxis.set_major_locator(MultipleLocator(1))
    ax.yaxis.set_major_locator(MultipleLocator(400))

    pyplot.grid(axis='y', color='lightgray', linestyle='--', linewidth=PlotConfig.grid_linewidth)
    pyplot.grid(axis='x', color='lightgray', linestyle='--', linewidth=PlotConfig.grid_linewidth)

    pyplot.ylim([800, 2000])

    ax.set_xlabel("Req rate (req/s)")
    ax.set_ylabel("Throughput (tokens/s)")
    pyplot.tight_layout()
    if PlotConfig.with_legend:
        fig.legend(loc=(0.52, 0.3), frameon=False, markerfirst=False)
    pyplot.savefig(output_path)


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--dataset", type=str, default="sharegpt")
    parser.add_argument("--model", type=str, default="meta-llama/Llama-2-7b-hf")
    parser.add_argument("--qps", nargs="+", type=str, default=["2", "2.5", "3", "3.5", "4", "4.5"])
    parser.add_argument("--numreqs", type=int, default=250)
    parser.add_argument(
        "--output-dir", type=str, default="evaluation/plots/diff_sm_performance"
    )
    parser.add_argument("--disagg-dir", type=str, default="outputs/e2e_perf/disagg")
    parser.add_argument("--offload-dir", type=str, default="outputs/e2e_perf/offload")
    parser.add_argument("--sm-partions", nargs="+", type=float, default=[0.1, 0.3, 0.5])
    parser.add_argument("--offload-ratios", nargs="+", type=float, default=[0.3, 0.7, 0.8])
    parser.add_argument("--num-execs", type=int, default=3)
    parser.add_argument("--crop-margin", action="store_true")
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    print(args)
    configs = [f"{args.dataset}-qps_{qps}-numreqs_{args.numreqs}" for qps in args.qps]
    qps_float_list = [float(qps) for qps in args.qps]
    offload_ratios: list[float] = args.offload_ratios
    sm_partitions: list[float] = args.sm_partions
    model: str = args.model
    default_sm_partition = 0.3
    offload_dirs = {
        sm_par: (
            f"{args.offload_dir}/sm_{sm_par}/ratio_{ratio}/{model}"
            if sm_par != default_sm_partition
            else f"{args.offload_dir}/ratio_{ratio}"
        )
        for sm_par, ratio in zip(sm_partitions, offload_ratios)
    }
    offload_ttfts = {
        sm_par: [
            get_ttft_for_config(config, offload_dirs[sm_par], args.num_execs)
            for config in configs
        ]
        for sm_par in sm_partitions
    }
    schemes, data = process_ttft(sm_partitions, offload_ttfts)
    plot_ttft_for_legend(qps_float_list, schemes, data, f"{args.output_dir}/{args.dataset}_ttft_vary_sm_partitions_with_legend.pdf")
    plot_ttft(qps_float_list, schemes, data, f"{args.output_dir}/{args.dataset}_ttft_vary_sm_partitions.pdf")

    offload_tpots = {
        sm_par: [
            get_tpot_for_config(config, offload_dirs[sm_par], args.num_execs)
            for config in configs
        ]
        for sm_par in sm_partitions
    }
    schemes, data = process_tpot(sm_partitions, offload_tpots)
    plot_tpot(qps_float_list, schemes, data, f"{args.output_dir}/{args.dataset}_tpot_vary_sm_partitioins.pdf")

    offload_p99_tpots = {
        sm_par: [
            get_p99_tpot_for_config(config, offload_dirs[sm_par], args.num_execs)
            for config in configs
        ]
        for sm_par in sm_partitions
    }
    schemes, data = process_p99_tpot(sm_partitions, offload_p99_tpots)
    plot_p99_tpot(qps_float_list, schemes, data, f"{args.output_dir}/{args.dataset}_p99_tpot_vary_sm_partitioins.pdf")

    offload_thpts = {
        sm_par: [
            get_thpt_for_config(config, offload_dirs[sm_par], args.num_execs)
            for config in configs
        ]
        for sm_par in sm_partitions
    }
    schemes, data = process_stable_thpt(sm_partitions, offload_thpts)
    plot_stable_thpt(
        qps_float_list, schemes, data, f"{args.output_dir}/{args.dataset}_thpt_vary_sm_partitioins.pdf"
    )

    if args.crop_margin:
        crop_path = crop_margins(f"{args.output_dir}/{args.dataset}_ttft_vary_sm_partitions.pdf")
        print(f"Cropped to {crop_path} successfully.")
        crop_path = crop_margins(f"{args.output_dir}/{args.dataset}_tpot_vary_sm_partitioins.pdf")
        print(f"Cropped to {crop_path} successfully.")
        crop_path = crop_margins(f"{args.output_dir}/{args.dataset}_thpt_vary_sm_partitioins.pdf")
        print(f"Cropped to {crop_path} successfully.")
        crop_path = crop_margins(f"{args.output_dir}/{args.dataset}_p99_tpot_vary_sm_partitioins.pdf")
        print(f"Cropped to {crop_path} successfully.")
        crop_path = crop_margins(f"{args.output_dir}/{args.dataset}_ttft_vary_sm_partitions_with_legend.pdf", "-p4 10 -1780 10 10")
        print(f"Cropped to {crop_path} successfully.")
