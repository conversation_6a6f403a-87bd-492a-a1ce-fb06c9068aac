#!/bin/bash
# sharegpt (debug use)
# python -m evaluation.plots.diff_sm_performance.plot_sharegpt_diff_sm --dataset sharegpt --sm-partions 0.1 0.3 0.5 --offload-ratios 0.3 0.7 0.8 --qps 1 1.5 2 2.5 3 3.5 4 4.5
# paper
python -m evaluation.plots.diff_sm_performance.plot_sharegpt_diff_sm \
    --dataset sharegpt \
    --sm-partions 0.1 0.3 0.5 \
    --offload-ratios 0.3 0.7 0.8 \
    --qps 2 2.5 3 3.5 4 4.5 \
    --crop-margin
