import argparse
import torch
import statistics

from dataclasses import dataclass
from matplotlib import pyplot as plt
from matplotlib.ticker import MultipleLocator
from pathlib import Path

from evaluation.plots.nsys.parser import NsysParser
from evaluation.plots.utils.plot_utils import sample_events, crop_margins

import matplotlib
matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.titlesize'] = 12
matplotlib.rcParams['axes.labelsize'] = 12
matplotlib.rcParams['xtick.labelsize'] = 12
matplotlib.rcParams['ytick.labelsize'] = 12
matplotlib.rcParams['legend.fontsize'] = 12

@dataclass
class PlotConfig:
    figsize = (2, 2)
    colors = ['#0000ED', '#BF0000', '#006521', 'black']
    marker_style = ['o', 's', '^', 'v', 'D', '<', '>', 's', 'x', '*']
    markersize = 6
    markeredgewidth = 1.5


def get_events_max_bandwidth(p: NsysParser, nvtx_name: str):
    events = p.get_nvtx_events(nvtx_name)
    sampled_events = sample_events(events)
    events_max_bandwidth = [
        torch.max(sampled_event.get_dram_bw()[0].to(float))
        for sampled_event in sampled_events
    ]
    mean_max_bandwidth = statistics.mean(
        [max_bandwidth.item() for max_bandwidth in events_max_bandwidth]
    )
    return mean_max_bandwidth


def plot_attn_bandwidth_with_sms(sm_pcts: list[int], attn_bw_pcts: list[float], output_path: str):
    # https://www.nvidia.com/content/dam/en-zz/Solutions/Data-Center/a100/pdf/nvidia-a100-datasheet-nvidia-us-2188504-web.pdf
    attn_bandwidths = [bw / 100 * 2039 for bw in attn_bw_pcts]
    # FIXME: using smCount to plot bandwidth
    plt.plot(sm_pcts, attn_bandwidths, '-^')
    plt.xlabel("sm core percentage/%")
    plt.ylabel("attention max achieved bandwidth/GB/s")
    plt.savefig(output_path)


def plot_attn_bandwidth_with_sms_small(
    sm_pcts: list[int],
    attn_bw_pcts: dict[tuple[int, int], list[float]],
    configs: list[tuple[int, int]],
    output_path: str,
):
    limit_line_color = '#BF0000'

    fig, ax = plt.subplots(figsize=(2, 2))

    marker_style = ['^', 'v', 'o', 'D', '<', '>', 's', 'x', '*']
    colors = ['C0', 'C1', 'C2', 'C4', 'C5', 'C6', 'C3']

    for idx, config in enumerate(configs):
        attn_bw_pct = attn_bw_pcts[config]
        # https://www.nvidia.com/content/dam/en-zz/Solutions/Data-Center/a100/pdf/nvidia-a100-datasheet-nvidia-us-2188504-web.pdf
        attn_bandwidths = [bw / 100 * 2 for bw in attn_bw_pct]
        ax.plot(
            sm_pcts,
            attn_bandwidths,
            marker=PlotConfig.marker_style[idx],
            color=PlotConfig.colors[idx],
            markersize=PlotConfig.markersize,
            markerfacecolor="none",
            markeredgewidth=PlotConfig.markeredgewidth,
            label=str(config[0]),
        )
    ax.plot([0, 100], [0, 2], color='black', linestyle='--')

    ax.set_xlabel('SM ratio (%)')
    ax.set_ylabel("Attn's HBM BW (TB/s)")

    ax.yaxis.set_major_locator(MultipleLocator(0.5))
    ax.xaxis.set_major_locator(MultipleLocator(25))

    plt.xlim([0, 100])
    plt.ylim([0, 2.3])

    plt.text(22, 2.08, 'HBM BW Limit', fontsize=10, color=limit_line_color)
    # plt.text(16, 0.1, 'BW Limit * SM Ratio', fontsize=10, color='black')

    # https://www.w3schools.com/python/matplotlib_grid.asp
    plt.grid(axis='y', color='lightgray', linestyle='--')
    plt.grid(axis='x', color='lightgray', linestyle='--')

    # plt.arrow(x=40, y=0.65, dx=5, dy=-0.3,
    #         #   length_includes_head=True,  # 箭头长度包含头部
    #         #   head_width=0.1, head_length=1,
    #           color='black', linewidth=1.5)

    # # 添加自定义箭头
    # plt.annotate(text="",
    #              xy=(34, 0.65), xytext=(45, 0.25),   # 箭头指向 (5, 5)，起始点为 (3, 3)
    #              arrowprops=dict(arrowstyle="->", color="black"))

    plt.axhline(2, color=limit_line_color)

    # plt.show()
    plt.savefig(output_path, bbox_inches='tight')
    print('saved in ' + output_path)
    output_path = crop_margins(output_path, "-p 5")
    print(f"Croped to {output_path} successfully.")


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--sm-pcts", nargs="+", type=int, default=list(range(10, 100 + 10, 10))
    )
    parser.add_argument("--seq-lens", nargs="+", type=int, default=[512, 1024, 2048])
    parser.add_argument("--batches", nargs="+", type=int, default=[16])
    parser.add_argument("--profile-dir", type=str, default="evaluation/results/attention_bandwidth")
    parser.add_argument(
        "--output-path",
        type=str,
        default="evaluation/plots/7_attention_bandwidth/attention_bandwidth.pdf",
    )
    parser.add_argument("--use-existed-data", action="store_true")
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    print(args)
    sm_pcts: list[int] = args.sm_pcts
    seq_lens: list[int] = args.seq_lens
    batches: list[int] = args.batches
    profile_dir: str = args.profile_dir
    output_path: str = args.output_path
    configs = [
        (seq_lens[0], batches[0]),
        (seq_lens[1], batches[0]),
        (seq_lens[2], batches[0]),
    ]

    if args.use_existed_data:
        sm_pcts = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100]
        attn_bw_pcts_with_sms = {
            (1024, 16): [31.0, 61.0, 76.0, 94.0, 97.0, 98.0, 98.0, 98.33333333333333, 98.0, 99.0]
        }
    else:
        profile_files = {
            (seq_len, batch, sm_pct): f"attention_sm_{sm_pct}_seqlen_{seq_len}_batch_{batch}.sqlite"
            for seq_len, batch in configs
            for sm_pct in sm_pcts
        }
        profile_paths = {
            key: Path(profile_dir, file) for key, file in profile_files.items()
        }
        nsys_parsers = {key: NsysParser(path) for key, path in profile_paths.items()}
        op_name = "decode.py:995(run)"
        attn_bw_pcts_with_sms = {
            (seq_len, batch): [
                get_events_max_bandwidth(
                    nsys_parsers[(seq_len, batch, sm_pct)], op_name
                )
                for sm_pct in sm_pcts
            ]
            for seq_len, batch in configs
        }

    plot_attn_bandwidth_with_sms_small(sm_pcts, attn_bw_pcts_with_sms, configs, output_path)
