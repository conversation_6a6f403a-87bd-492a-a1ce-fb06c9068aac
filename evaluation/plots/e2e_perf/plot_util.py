import argparse
import os
import itertools
import matplotlib.pyplot as plt
import numpy
import pathlib
import pprint
import glob
import torch
import logging

from typing import Any, Optional, List
from evaluation.plots.nsys import utils, sqlite_parser
from evaluation.plots.nsys.parser import NsysParser, Event
from evaluation.plots.nsys.utils import MetricNames

from dataclasses import dataclass
from matplotlib.ticker import MultipleLocator
from evaluation.plots.utils.plot_utils import SYS_NAME


scheme_to_label = {
    SYS_NAME: SYS_NAME,
    "vllm": "vLLM",
}

def plot_util_small(target_offload_ratio, data, schemes, cata, ytitle, loc, save_path):
    fig, ax1 = plt.subplots(figsize=(3, 2))
    # ax2 = ax1.twinx()

    x = numpy.arange(len(target_offload_ratio)) # the label locations
    width = 0.16  # the width of the bars
    offsets = numpy.arange(len(schemes)) * width - width * (len(schemes) - 1) / 2
    offsets = offsets * 1.3 # add spaces for bars in a group
    logging.info(offsets)

    patterns = [None, "////", "\\\\\\\\", "xxxx", "oooo", "////", "\\\\\\\\", "xxxx", None]

    # colors = [str(x) for x in [1, 0.9, 0.9, 0.9, 0.9, 0.45, 0.45, 0.45, 0]]

    # https://matplotlib.org/stable/users/prev_whats_new/dflt_style_changes.html
    # colors = ['C0', 'C1', 'C2', 'C4', 'C5', 'C6', 'C3']
    colors = ['C0', 'C6', 'C2', 'C4', 'C5', 'C6', 'C3']

    group_labels = []
    for i in range(len(schemes)):
        scheme = schemes[i]
        tag = scheme_to_label[scheme]
        print(scheme, tag)
        group_labels.append(tag)

        # plot data for target workloads
        # target_ys = [data[cata][scheme][idx] for idx in workload_idxs]
        target_ys = data[cata][scheme]

        ax1.bar(x + offsets[i], target_ys, width,
            label=tag, hatch=patterns[i], color='white', edgecolor=colors[i], linewidth=1)

    plt.xticks(x, target_offload_ratio)

    ax1.set_xlabel('Offloading ratio (%)')
    ax1.set_ylabel(ytitle)

    plt.text(0.5, 85, 'prefill', color='blue')
    plt.text(3.2, 85, 'decoding', color='blue')

    ax1.set_ylim([0, 100])
    print(f"{group_labels=}")

    ax1.legend(group_labels, ncol=2, loc=loc, fontsize=9)

    plt.axvline(2.5, linestyle="--", color='blue')

    # plt.show()
    plt.savefig(save_path, bbox_inches='tight')
    logging.info('saved in ' + save_path)

def parse_args():
    parser = argparse.ArgumentParser()
    # parser.add_argument("--reports-dir", type=str, default="plots/1_1_prefill_util/1_1_prefill_util_latest")
    parser.add_argument("--reports-dir", type=str, default="evaluation/plots/3_1_prefill_kernel/3_1_prefill_kernel_0114")
    parser.add_argument("--split-tag", type=str, default="3_1_prefill_kernel_")
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    # prompt_lens, data = get_data(args)

    # 0131
    # PD_ratio = ['1:1', '2:1', '3:1', '1:1', '2:1', '3:1']
    offload_ratio = ['10', '20', '30', '10', '20', '30']
    # data = {'comp': {'vllm': [50, 50, 50, 20, 20, 20], SYS_NAME: [60, 60, 60, 30, 30, 30]}, 'bws': {'vllm': [20, 20, 20, 50, 50, 50], SYS_NAME: [40, 40, 40, 50, 50, 50]}}

    data = {'comp': {'vllm': [73.8, 73.8, 73.8, 14.4, 14.4, 14.4], SYS_NAME: [52.7, 52.3, 51.3, 13.2, 14.1, 15.7]}, 'bws': {'vllm': [22.6, 22.6, 22.6, 73.5, 73.5, 73.5], SYS_NAME: [22.0, 27.8, 34.0, 70.2, 69.4, 68.8]}}

    # targeted_PD_ratio = ['1:1', '2:1', '3:1', '1:1', '2:1', '3:1']
    targeted_offload_ratio = ['10', '20', '30', '10', '20', '30']
    scheme_order = ["vllm", SYS_NAME]
    plot_util_small(targeted_offload_ratio, data, scheme_order, 'comp', 'Compute Util. (%)', (0.05, 0.3), 'evaluation/plots/e2e_perf/comp_util_small_1.pdf')
    plot_util_small(targeted_offload_ratio, data, scheme_order, 'bws', 'HBM BW Util. (%)', (0.05, 0.4), 'evaluation/plots/e2e_perf/bw_util_small_1.pdf')
