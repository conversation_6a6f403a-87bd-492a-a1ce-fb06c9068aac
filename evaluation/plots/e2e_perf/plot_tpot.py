import argparse
import os
import itertools
import matplotlib.pyplot as plt
import numpy
import pathlib
import pprint
import glob
import torch
import logging

from typing import Any, Optional, List
from evaluation.plots.nsys import utils, sqlite_parser
from evaluation.plots.nsys.parser import NsysParser, Event
from evaluation.plots.nsys.utils import MetricNames
from evaluation.plots.utils.plot_utils import SYS_NAME

from dataclasses import dataclass
from matplotlib.ticker import MultipleLocator


scheme_to_label = {
    SYS_NAME: SYS_NAME,
    "vllm": "vLLM",
}

def plot_decode_small(target_offload_ratio, data, schemes, save_path):
    fig, ax1 = plt.subplots(figsize=(3, 2))
    ax2 = ax1.twinx()

    x = numpy.arange(len(target_offload_ratio)) # the label locations
    width = 0.16  # the width of the bars
    offsets = numpy.arange(len(schemes)) * width - width * (len(schemes) - 1) / 2
    offsets = offsets * 1.3 # add spaces for bars in a group
    logging.info(offsets)

    patterns = [None, "////", "\\\\\\\\", "xxxx", "oooo", "////", "\\\\\\\\", "xxxx", None]

    # colors = [str(x) for x in [1, 0.9, 0.9, 0.9, 0.9, 0.45, 0.45, 0.45, 0]]

    # https://matplotlib.org/stable/users/prev_whats_new/dflt_style_changes.html
    # colors = ['C0', 'C1', 'C2', 'C4', 'C5', 'C6', 'C3']
    colors = ['C0', 'C6', 'C2', 'C4', 'C5', 'C6', 'C3']

    group_labels = []
    for i in range(len(schemes)):
        scheme = schemes[i]
        tag = scheme_to_label[scheme]
        print(scheme, tag)
        group_labels.append(tag)

        target_ys = data["tpot"][scheme]

        ax1.bar(x + offsets[i], target_ys, width,
            label=tag, hatch=patterns[i], color='white', edgecolor=colors[i], linewidth=1)

    for i in range(len(target_offload_ratio)):
        x_values = [o + x[i] for o in offsets]
        y_idx = i
        y_values = [data['throughput'][scheme][y_idx]/1000 for scheme in schemes]

        ax2.plot(x_values, y_values, marker='D', markersize=3, color='red', lw=1)

    plt.xticks(x, target_offload_ratio)

    # ax1.set_xlabel('P-D ratios')
    ax1.set_xlabel('Offloading ratio (%)')
    ax1.set_ylabel('TPOT (ms)')

    ax2.set_ylabel('Output token throughput\n(K tokens/s)', color='red')
    ax2.yaxis.label.set_color('red')

    plt.text(1, 1.5, '7B', color='blue')
    plt.text(4, 1.5, '13B', color='blue')
    plt.axvline(2.5, linestyle="--", color='blue')

    ax1.set_ylim([0, 80])
    ax2.set_ylim([0, 2])

    ax1.legend(group_labels, ncol=2, loc='lower center', fontsize=9)

    # plt.show()
    plt.savefig(save_path, bbox_inches='tight')
    logging.info('saved in ' + save_path)

def parse_args():
    parser = argparse.ArgumentParser()
    # parser.add_argument("--reports-dir", type=str, default="plots/1_1_prefill_util/1_1_prefill_util_latest")
    parser.add_argument("--reports-dir", type=str, default="evaluation/plots/3_1_prefill_kernel/3_1_prefill_kernel_0114")
    parser.add_argument("--split-tag", type=str, default="3_1_prefill_kernel_")
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    # prompt_lens, data = get_data(args)

    # 0131
    # PD_ratio = ['1:1', '2:1', '3:1', '1:1', '2:1', '3:1']
    offload_ratio = ['10', '20', '30', '10', '20', '30']
    data = {'ttft': {'vllm': [2928, 2928, 2928, 7095, 7095, 7095], SYS_NAME: [1627, 735, 484, 3345, 2654, 4179]}, 'tpot': {'vllm': [35.5, 35.5, 35.5, 43, 43, 43], SYS_NAME: [37.2, 36.96, 36.02, 46, 45, 45]}, 'throughput': {'vllm': [1089, 1089, 1089, 492, 492, 492], SYS_NAME: [1102.8, 1159.3, 1189.0, 501, 524, 508]}}

    # targeted_PD_ratio = ['1:1', '2:1', '3:1', '1:1', '2:1', '3:1']
    target_offload_ratio = ['10', '20', '30', '10', '20', '30']
    scheme_order = ["vllm", SYS_NAME]
    plot_decode_small(target_offload_ratio, data, scheme_order, 'evaluation/plots/e2e_perf/decode_small.pdf')
