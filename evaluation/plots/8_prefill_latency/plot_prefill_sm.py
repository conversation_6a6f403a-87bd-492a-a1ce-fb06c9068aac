import argparse
import torch
import statistics

from dataclasses import dataclass
from pathlib import Path

import matplotlib.pyplot as plt
from matplotlib.ticker import MultipleLocator

import numpy as np

from evaluation.plots.nsys.parser import NsysParser
from evaluation.plots.utils.plot_utils import sample_events

import matplotlib
matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.titlesize'] = 12
matplotlib.rcParams['axes.labelsize'] = 12
matplotlib.rcParams['xtick.labelsize'] = 12
matplotlib.rcParams['ytick.labelsize'] = 12
matplotlib.rcParams['legend.fontsize'] = 10

@dataclass
class PlotConfig:
    figsize = (2, 2)
    colors = ['#0000ED', '#BF0000', '#006521', 'black']
    marker_style = ['o', 's', '^', 'v', 'D', '<', '>', 's', 'x', '*']
    markersize = 6
    markeredgewidth = 1.5

def extract_record(lines):
    n_times = len(lines) - 1
    print(f'{lines[0]=}')
    _, prompt_len, sm_ratio = lines[0].split(' ')
    latencies = sorted([float(x) for x in lines[1:]])
    idx = len(latencies)//2
    res_latency = latencies[idx]
    if res_latency - latencies[0] > 0.1:
        idx -= 1
        res_latency = latencies[idx]
        assert idx >= 0
    print(f'{prompt_len=}, {sm_ratio=}, {idx=}, {res_latency=}')
    return int(prompt_len), int(sm_ratio), res_latency

def get_data(args):
    lines = []
    with open(args.record_path, "r") as f:
        lines = f.read().strip().split('\n')
    assert (len(lines) % (args.n_times + 1)) == 0
    data = {}
    for i in range(0, len(lines), args.n_times + 1):
        end_line_no = i + args.n_times + 1
        prompt_len, sm_ratio, latency = extract_record(lines[i:end_line_no])
        if prompt_len not in data:
            data[prompt_len] = [(sm_ratio, latency)]
        else:
            data[prompt_len].append((sm_ratio, latency))
    
    res = {}
    for prompt_len in data:
        data[prompt_len] = sorted(data[prompt_len], key=lambda x: x[0])
        base_value = data[prompt_len][-1][1]
        res[prompt_len] = []
        res[prompt_len].append([x[0] for x in data[prompt_len]])
        res[prompt_len].append([x[1] / base_value for x in data[prompt_len]])

    print(data)
    return res


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--n-times", type=int, default=5)
    parser.add_argument(
        "--record-path",
        type=str,
        default="evaluation/results/8_prefill_latency/prefill_latency_2025-01-29_00-30-37.txt",
    )
    parser.add_argument(
        "--output-path",
        type=str,
        default="evaluation/plots/8_prefill_latency/prefill_sm.pdf",
    )
    args = parser.parse_args()
    return args


def get_events_max_bandwidth(p: NsysParser, nvtx_name: str):
    events = p.get_nvtx_events(nvtx_name)
    sampled_events = sample_events(events)
    events_max_bandwidth = [
        torch.max(sampled_event.get_dram_bw()[0].to(float))
        for sampled_event in sampled_events
    ]
    mean_max_bandwidth = statistics.mean(
        [max_bandwidth.item() for max_bandwidth in events_max_bandwidth]
    )
    return mean_max_bandwidth

def plot_latency_with_various_sm_small(data, scheme_order: list[int], output_path: str):
    fig, ax = plt.subplots(figsize=PlotConfig.figsize)

    for i, scheme in enumerate(scheme_order):
        # scheme, scatters = t
        scatters = data[scheme]
        ax.plot(
            scatters[0],
            scatters[1],
            marker=PlotConfig.marker_style[i],
            color=PlotConfig.colors[i],
            markersize=PlotConfig.markersize,
            markerfacecolor="none",
            markeredgewidth=PlotConfig.markeredgewidth,
            label=str(scheme),
        )
    ax.plot([50, 100], [2, 1], color='black', linestyle='--')

    # x = np.linspace(1, 100, 100)
    # y = 100/x
    # ax.plot(x, y, color='black', linestyle='--')

    ax.set_xlabel('SM ratio (%)')
    ax.set_ylabel("Normalized Latency")

    ax.xaxis.set_major_locator(MultipleLocator(10))
    ax.yaxis.set_major_locator(MultipleLocator(0.25))

    plt.xlim([100, 50])
    plt.ylim([1, 2])

    # https://www.w3schools.com/python/matplotlib_grid.asp
    plt.grid(axis='y', color='lightgray', linestyle='--')
    plt.grid(axis='x', color='lightgray', linestyle='--')

    # fig.legend(loc=(0.30, 0.56), frameon=False, handletextpad=0.6)  # 设置背景, with ax.text
    fig.legend(loc=(0.30, 0.60), frameon=False, handletextpad=0.6)  # 设置背景

    # plt.text(95, 1.90, 'Latency / SM Ratio', fontsize=10, color='black')

    # # 添加自定义箭头
    # plt.annotate(text="",
    #              xy=(58, 1.75), xytext=(65, 1.85),   # 箭头指向 xy，起始点为 xytext
    #              arrowprops=dict(arrowstyle="->", color="black"))

    # plt.show()
    plt.savefig(output_path, bbox_inches='tight')
    print('saved in ' + output_path)

def plot_latency_with_various_sm_small_new(data, scheme_order: list[int], output_path: str):
    fig, ax = plt.subplots(figsize=PlotConfig.figsize)

    marker_style = ['^', 'v', 'o', 'D', '<', '>', 's', 'x', '*']
    colors = ['C0', 'C1', 'C2', 'C4', 'C5', 'C6', 'C3']

    for i, scheme in enumerate(scheme_order):
        # scheme, scatters = t
        scatters = data[scheme]
        rev_x = [100/x for x in scatters[0]]
        ax.plot(rev_x, scatters[1],
            marker=PlotConfig.marker_style[i], color=PlotConfig.colors[i],
            markersize=PlotConfig.markersize, markerfacecolor='none',
            markeredgewidth=PlotConfig.markeredgewidth,
            label=str(scheme))
    # ax.plot([50, 100], [2, 1], color='black', linestyle='--')
    # ax.plot([0, 100], [2, 1], color='black', linestyle='--')

    # x = np.linspace(1, 100, 100)
    # y = 100/x
    # ax.plot(x, y, color='black', linestyle='--')
    ax.plot([1, 2], [1, 2], color='black', linestyle='--')

    ax.set_xlabel('1/SM ratio')
    ax.set_ylabel("Normalized Latency")

    ax.xaxis.set_major_locator(MultipleLocator(0.25))
    ax.yaxis.set_major_locator(MultipleLocator(0.25))

    plt.xlim([1, 2])
    plt.ylim([1, 2])

    # plt.text(58, 1.9, 'linear prop. TTFT', color='red')
    # plt.text(22, 120, 'linear proportional HBM BW', color=colors[2])

    # https://www.w3schools.com/python/matplotlib_grid.asp
    plt.grid(axis='y', color='lightgray', linestyle='--')
    plt.grid(axis='x', color='lightgray', linestyle='--')

    # fig.legend(loc=(0.60, 0.60), frameon=False, markerfirst=False)  # 设置背景
    fig.legend(loc=(0.30, 0.56), frameon=False, handletextpad=0.6)  # 设置背景

    plt.text(1.05, 1.90, 'Latency / SM Ratio', fontsize=10, color='black')

    # # 添加自定义箭头
    # plt.annotate(text="",
    #              xy=(58, 1.75), xytext=(65, 1.85),   # 箭头指向 xy，起始点为 xytext
    #              arrowprops=dict(arrowstyle="->", color="black"))

    # plt.legend(loc=(0.49, 0.55), facecolor='white', framealpha=1.0, frameon=False)
    # fig.tight_layout()

    # plt.show()
    plt.savefig(output_path, bbox_inches='tight')
    print('saved in ' + output_path)

def plot_latency_with_various_sm_small_v3(data, scheme_order: list[int], output_path: str):
    fig, ax = plt.subplots(figsize=PlotConfig.figsize)

    marker_style = ['^', 'v', 'o', 'D', '<', '>', 's', 'x', '*']
    colors = ['C0', 'C1', 'C2', 'C4', 'C5', 'C6', 'C3']

    print(f'{data=}')

    for i, scheme in enumerate(scheme_order):
        # scheme, scatters = t
        scatters = data[scheme]
        rev_y = [1/y for y in scatters[1]]
        ax.plot(scatters[0], rev_y,
            marker=PlotConfig.marker_style[i], color=PlotConfig.colors[i],
            markersize=PlotConfig.markersize, markerfacecolor='none',
            markeredgewidth=PlotConfig.markeredgewidth,
            label=str(scheme))

    ax.plot([50, 100], [0.5, 1.0], color='black', linestyle='--')

    ax.set_xlabel('SM ratio')
    ax.set_ylabel("Normalized Throughput")

    ax.xaxis.set_major_locator(MultipleLocator(10))
    ax.yaxis.set_major_locator(MultipleLocator(0.10))

    plt.xlim([100, 30])
    plt.ylim([0.3, 1])

    # plt.text(58, 1.9, 'linear prop. TTFT', color='red')
    # plt.text(22, 120, 'linear proportional HBM BW', color=colors[2])

    # https://www.w3schools.com/python/matplotlib_grid.asp
    plt.grid(axis='y', color='lightgray', linestyle='--')
    plt.grid(axis='x', color='lightgray', linestyle='--')

    # fig.legend(loc=(0.58, 0.36), frameon=False, markerfirst=False, handletextpad=0.6)  # 设置背景

    # plt.text(58, 0.52, 'Throughput * SM Ratio', fontsize=9, color='black')

    # # 添加自定义箭头
    # plt.annotate(text="",
    #              xy=(65, 0.65), xytext=(70, 0.55),   # 箭头指向 xy，起始点为 xytext
    #              arrowprops=dict(arrowstyle="->", color="black"))
    fig.legend(loc=(0.25, 0.36), frameon=False, markerfirst=False, handletextpad=0.6)  # 设置背景

    plt.text(98, 0.52, 'Throughput * SM Ratio', fontsize=9, color='black')

    # 添加自定义箭头
    plt.annotate(text="",
                 xy=(65, 0.65), xytext=(70, 0.55),   # 箭头指向 xy，起始点为 xytext
                 arrowprops=dict(arrowstyle="->", color="black"))


    # plt.show()
    plt.savefig(output_path, bbox_inches='tight')
    print('saved in ' + output_path)

if __name__ == "__main__":
    args = parse_args()
    data = get_data(args)
    print(data)

    scheme_order = [512, 1024, 4096]
    plot_latency_with_various_sm_small(data, scheme_order, args.output_path)
    # plot_latency_with_various_sm_small_v3(data, scheme_order, "evaluation/plots/8_prefill_latency/prefill_sm.pdf")
