import argparse
import matplotlib.pyplot as plt
import numpy
import glob
import torch
import logging

from typing import Any, Optional, List
from evaluation.plots.nsys.parser import NsysParser, Event
from evaluation.plots.nsys.utils import MetricNames
from evaluation.plots.utils.plot_utils import crop_margins, CommonColors
from evaluation.plots.utils.model_utils import (
    LLAMA2_7B,
    ModelOperator,
    get_gemm_compute_flops,
    get_attn_compute_flops,
)
from evaluation.plots.nsys.utils import A100_80GB_META_DATA


from dataclasses import dataclass
from matplotlib.ticker import MultipleLocator

@dataclass
class PlotConfig:
    figsize = (3.2, 2)
    colors = CommonColors.gradient_blues
    marker_style = ['o', 's', '^', 'v', 'D', '<', '>', 's', 'x', '*']
    markersize = 5
    markeredgewidth = 0.5
    bar_width = 0.13  # the width of the bars
    bar_padding = 1.4 # add spaces for bars in a group


import matplotlib
matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.titlesize'] = 12
matplotlib.rcParams['axes.labelsize'] = 12
matplotlib.rcParams['xtick.labelsize'] = 10
matplotlib.rcParams['ytick.labelsize'] = 10
matplotlib.rcParams['legend.fontsize'] = 12

scheme_to_label = {
    'qkv': 'QKV_proj',
    'attn': 'Attn',
    'o': 'O_proj',
    'ffn': 'FFN',
}

@dataclass
class DecodeKernelPoints:
    layer_line_num: str = "llama.py:244(forward)"
    qkv_line_num: str = "linear.py:366(forward)"
    attn_line_num: str = "decode.py:942(forward)"
    o_line_num: str = "linear.py:1066(forward)"
    ffn1_line_num: str = "linear.py:366(forward)"
    ffn2_line_num: str = "linear.py:1066(forward)"

class ReportKernelPoints:

    def __init__(self, layer: str, qkv: str, attn: str, o: str,
                 ffn1: str, ffn2: str):
        self.layer_line_num = layer
        self.qkv_line_num = qkv
        self.attn_line_num = attn
        self.o_line_num = o
        self.ffn1_line_num = ffn1
        self.ffn2_line_num = ffn2

def get_global_id(p: NsysParser, kernel_points: ReportKernelPoints):
    attn_es = p.get_nvtx_events(kernel_points.attn_line_num)
    first_attn_e = attn_es[0]
    thread_id = first_attn_e['globalTid'] % 0x1000000
    global_pid = (first_attn_e['globalTid'] // 0x1000000) * 0x1000000
    print('>>> thread_id =', thread_id, 'from', first_attn_e['globalTid'])
    return thread_id, first_attn_e['globalTid'], global_pid

def get_kernel_stats(profile_path: str,
                     kernel_points: ReportKernelPoints,
                     roi_start: Optional[int] = None,
                     roi_end: Optional[int] = None,
                     split_tag: str = ''):
    print(f'start processing {profile_path}')
    p = NsysParser(profile_path)

    filename = profile_path.rsplit(split_tag, 1)[-1]
    prompt_len = int(filename.split('_', 1)[0])

    # get thread id
    _, global_tid, global_pid = get_global_id(p, kernel_points)

    # set thread ID for parser
    p.set_global_tid_for_query(global_tid)

    es = p.get_nvtx_events(kernel_points.layer_line_num, roi_start, roi_end)
    # print(pprint.pformat(es[:4]))
    qkv_latencies, attn_latencies = [], []
    o_latencies = []
    ffn1_latencies = []
    ffn2_latencies = []
    ffn_latencies = []
    layer_latencies = []

    qkv_bws = ()
    attn_bws = ()
    o_bws = ()
    ffn1_bws = ()
    ffn2_bws = ()
    ffn_bws = ()

    qkv_comps = ()
    attn_comps = ()
    o_comps = ()
    ffn1_comps = ()
    ffn2_comps = ()
    ffn_comps = ()
    for idx, layer_e in enumerate(es[:4]):
        # set global IDs for events
        layer_e.set_global_tid_for_query(global_tid)
        layer_e.set_global_pid_for_query(global_pid)

        # skip es[0] to avoid cold start effect
        if idx == 0:
            continue
        # print('start_time from', es[idx - 1])
        start_time = es[idx - 1]['end']
        # print('>>>>>> curr event', pprint.pformat(es[idx]))
        # print('end_time from', es[idx + 1])
        end_time = es[idx + 1]['start'] if idx < len(es) - 1 else None
        # print(f'[start_time, end_time] {start_time, end_time}')
        linear_es = p.get_nvtx_events("linear", start_time, end_time)
        # print('linear_es', pprint.pformat(linear_es))
        assert len(linear_es) == 4, {len(linear_es)}

        # set thread ID for events
        for linear_e in linear_es:
            linear_e.set_global_tid_for_query(global_tid)
            linear_e.set_global_pid_for_query(global_pid)

        latencies = [
            (li.cuda_end_time - li.cuda_start_time) for li in linear_es
        ]

        # print('latencies', pprint.pformat(latencies))
        qkv_latencies.append(latencies[0])
        o_latencies.append(latencies[1])
        ffn1_latencies.append(latencies[2])
        ffn2_latencies.append(latencies[3])

        qkv_bws += (p.get_events_dram_bw(linear_es[:1])[0],)
        o_bws += (p.get_events_dram_bw(linear_es[1:2])[0],)
        ffn1_bws += (p.get_events_dram_bw(linear_es[2:3])[0],)
        ffn2_bws += (p.get_events_dram_bw(linear_es[3:])[0],)

        qkv_comps += (p.get_events_tc_active(linear_es[:1])[0],)
        o_comps += (p.get_events_tc_active(linear_es[1:2])[0],)
        ffn1_comps += (p.get_events_tc_active(linear_es[2:3])[0],)
        ffn2_comps += (p.get_events_tc_active(linear_es[3:])[0],)

        # extract attn stats
        attn_es = p.get_nvtx_events(kernel_points.attn_line_num, start_time, end_time)
        assert len(attn_es) == 1, {len(attn_es)}
        attn_e = attn_es[0]

        # set thread ID for events
        attn_e.set_global_tid_for_query(global_tid)
        attn_e.set_global_pid_for_query(global_pid)

        attn_latency = attn_e.cuda_end_time - attn_e.cuda_start_time
        attn_latencies.append(attn_latency)
        attn_bws += (p.get_events_dram_bw(attn_es)[0],)
        attn_comps += (p.get_events_tc_active(attn_es)[0],)

        # from pprint import pformat
        # print(f"idx: {idx}, {pformat(e)}")
        layer_latencies.append(layer_e.cuda_end_time - layer_e.cuda_start_time)
    
    ffn_latencies = [x1 + x2 for x1, x2 in zip(ffn1_latencies, ffn2_latencies)]
    ffn_bws = [torch.cat((x1, x2)) for x1, x2 in zip(ffn1_bws, ffn2_bws)]
    ffn_comps = [torch.cat((x1, x2)) for x1, x2 in zip(ffn1_comps, ffn2_comps)]

    agg_latencies = {
        # devide 1e6 to express in mircosecond (ms)
        "layer": numpy.mean(layer_latencies) / 1e6,
        "qkv": numpy.mean(qkv_latencies) / 1e6,
        "attn": numpy.mean(attn_latencies) / 1e6,
        "o": numpy.mean(o_latencies) / 1e6,
        "ffn": numpy.mean(ffn_latencies) / 1e6,
    }

    def _get_mean_metric_value(values: List[torch.Tensor]):
        tmp = torch.cat(values)
        return (torch.sum(tmp) / tmp.numel()).item()

    agg_bws = {
        "qkv": _get_mean_metric_value(qkv_bws),
        "attn": _get_mean_metric_value(attn_bws),
        "o": _get_mean_metric_value(o_bws),
        "ffn": _get_mean_metric_value(ffn_bws),
    }

    agg_comps = {
        "qkv": _get_mean_metric_value(qkv_comps),
        "attn": _get_mean_metric_value(attn_comps),
        "o": _get_mean_metric_value(o_comps),
        "ffn": _get_mean_metric_value(ffn_comps),
    }

    stats = {
        "latency": agg_latencies,
        "bws": agg_bws,
        "comps": agg_comps
    }

    return prompt_len, stats

def get_data(args):
    TestReportKernelPoints = DecodeKernelPoints
    trace_info = ReportKernelPoints(
        layer=TestReportKernelPoints.layer_line_num,
        qkv=TestReportKernelPoints.qkv_line_num,
        attn=TestReportKernelPoints.attn_line_num,
        o=TestReportKernelPoints.o_line_num,
        ffn1=TestReportKernelPoints.ffn1_line_num,
        ffn2=TestReportKernelPoints.ffn2_line_num
    )
    # res = get_kernel_stats('plots/1_1_prefill_util/1_1_prefill_util_latest/report_1_1_prefill_util_1280_2025-01-10_23-34-40.sqlite', trace_info, split_tag='1_1_prefill_util_')
    # print(">>>>>>> res: ", res)
    kernel_lists = ['qkv', 'attn', 'o', 'ffn']
    data = {
        "latency": {},
        "bws": {},
        "comps": {}
    }
    for kernel in kernel_lists:
        data['latency'][kernel] = []
        data['bws'][kernel] = []
        data['comps'][kernel] = []

    results = []
    for rpath in glob.glob(f"{args.reports_dir}/*.sqlite"):
        res = get_kernel_stats(rpath, trace_info, split_tag=args.split_tag)
        print(">>>>>>> res: ", res)
        results.append(res)

    results = sorted(results, key=lambda x: x[0])
    batches = []
    for batch, stats in results:
        batches.append(batch)
        layer_latency = stats["latency"]["layer"]
        for kernel in kernel_lists:
            data["latency"][kernel].append(stats["latency"][kernel]/layer_latency*100)
            data["bws"][kernel].append(stats["bws"][kernel])
            if kernel == "qkv":
                tflops = get_gemm_compute_flops(LLAMA2_7B, ModelOperator.QKV_PROJ, batch) / 1e12  # TFLOPS
            elif kernel == "attn":
                tflops = get_attn_compute_flops(LLAMA2_7B, batch, 1024) / 1e12  # TFLOPS
            elif kernel == "o":
                tflops = get_gemm_compute_flops(LLAMA2_7B, ModelOperator.O_PROJ, batch) / 1e12  # TFLOPS
            elif kernel == "ffn":
                tflops = (
                    get_gemm_compute_flops(LLAMA2_7B, ModelOperator.FFN0, batch)
                    + get_gemm_compute_flops(LLAMA2_7B, ModelOperator.FFN1, batch)
                ) / 1e12  # TFLOPS
            latency_s = stats["latency"][kernel] / 1e3  # s
            compute_stat = tflops / latency_s
            compute_pct = compute_stat / A100_80GB_META_DATA.PEAK_FP16_TFLOPS * 100
            data["comps"][kernel].append(compute_pct)

    print(f'batches: {batches}')
    print(f'data: {data}')
    return batches, data

def plot_kernel_util_small(target_workloads, workload_names, data, schemes, category, ylabel, save_path):
    fig, ax = plt.subplots(figsize=PlotConfig.figsize)

    x = numpy.arange(len(schemes)) # the label locations
    width = PlotConfig.bar_width  # the width of the bars
    offsets = numpy.arange(len(target_workloads)) * width - width * (len(target_workloads) - 1) / 2
    offsets = offsets * PlotConfig.bar_padding # add spaces for bars in a group
    logging.info(offsets)

    plot_with_legend = 'legend' in save_path

    group_labels = []
    for i in range(len(target_workloads)):
        scheme = schemes[i]
        tag = target_workloads[i]
        print(scheme, tag)
        group_labels.append(scheme_to_label[scheme])

        # plot data for target workloads
        idx = workload_names.index(tag)
        target_value = [
            data[category][scheme][idx] for scheme in schemes
        ]

        label = f'Batch: {tag}'

        # plt.bar(x + offsets[i], target_value, width,
        #     label=tag, hatch=patterns[i], color=colors[i], edgecolor='black', linewidth=0.5)
        plt.bar(x + offsets[i], target_value, width,
            label=label, color=PlotConfig.colors[i], edgecolor='black', linewidth=0.5)

    plt.xticks(x, group_labels)

    # plt.xlabel('Batch size')
    plt.ylabel(ylabel)

    plt.ylim([0, 100])

    plt.grid(axis='y', color='lightgray', linestyle='--')
    ax.set_axisbelow(True)

    if plot_with_legend:
        fig.legend(ncol=4, loc='upper center', frameon=False)
        fig.subplots_adjust(top=0.75)

    # plt.show()
    plt.savefig(save_path, bbox_inches='tight')
    logging.info('saved in ' + save_path)

def plot_kernel_mem_util_small(target_workloads, workload_names, data, schemes, save_path, plot_time=False):
    fig, ax1 = plt.subplots(figsize=PlotConfig.figsize)
    if plot_time:
        ax2 = ax1.twinx()

    x = numpy.arange(len(schemes)) # the label locations
    width = PlotConfig.bar_width  # the width of the bars
    offsets = numpy.arange(len(target_workloads)) * width - width * (len(target_workloads) - 1) / 2
    offsets = offsets * PlotConfig.bar_padding # add spaces for bars in a group
    logging.info(offsets)

    # Obtain the index of target_workloads in workload_names
    workload_idxs = [workload_names.index(x) for x in target_workloads]

    group_labels = []
    for i in range(len(schemes)):
        scheme = schemes[i]
        tag = target_workloads[i]
        print(scheme, tag)
        group_labels.append(scheme_to_label[scheme])

        # plot data for target workloads
        idx = workload_names.index(tag)
        target_bws = [
            data['bws'][scheme][idx] for scheme in schemes
        ]

        # ax1.bar(x + offsets[i], target_bws, width,
        #     label=tag, hatch=patterns[i], color=colors[i], edgecolor='black', linewidth=0.5)
        ax1.bar(x + offsets[i], target_bws, width,
            label=tag, color=PlotConfig.colors[i], edgecolor='black', linewidth=0.5)

        # ax2.plot(x + offsets[i], data["latency"][scheme], marker='D', markersize=3, color='red')

    if plot_time:
        for i, workload in enumerate(target_workloads):
            x_values = [o + x[i] for o in offsets]
            y_idx = workload_names.index(workload)
            y_values = [data['latency'][scheme][y_idx] for scheme in schemes]

            ax2.plot(x_values, y_values, marker='D', markersize=PlotConfig.markersize,
                    markerfacecolor='none', markeredgewidth=PlotConfig.markeredgewidth,
                    color='black', lw=0.5)
        ax2.set_ylabel('Execute Time Ratio (%)', color='red')
        ax2.yaxis.label.set_color('red')
        ax2.set_ylim([0, 100])
        ax2.set_axisbelow(True)

    plt.xticks(x, group_labels)

    # ax1.set_xlabel('Batch size')
    ax1.set_ylabel('Mem BW Util. (%)')

    ax1.set_ylim([0, 100])

    # ax1.legend(group_labels, ncol=4, loc='upper center', fontsize=9)
    plt.grid(axis='y', color='lightgray', linestyle='--')
    ax1.set_axisbelow(True)

    # plt.show()
    plt.savefig(save_path, bbox_inches='tight')
    logging.info('saved in ' + save_path)

def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--reports-dir", type=str, default="evaluation/results/3_2_decode_kernel")
    parser.add_argument("--split-tag", type=str, default="3_2_decode_kernel_")
    parser.add_argument("--use-existed-data", action="store_true")
    parser.add_argument("--no-crop-margin", action="store_true")
    parser.add_argument(
        "--output-dir",
        type=str,
        default="evaluation/plots/3_2_decode_kernel",
    )
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    if args.use_existed_data:
        # # 0114, batch size 8~88
        # batch_sizes = [8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88]
        # data = {'latency': {'qkv': [3.311983571132207, 3.19740500463392, 3.2495328522503995, 2.9970203162310463, 3.3671214284266053, 3.2901849519683535, 3.677611611531923, 3.2869734680548435, 3.801374160629322, 3.6971453799554044, 3.5556213565281762], 'attn': [26.750998990865437, 29.81350822254355, 29.859818624405804, 34.044997657871996, 41.58076523535382, 45.007241905977544, 54.80386520332362, 56.686983552234274, 62.28762667624659, 63.31640548363572, 65.53436352981093], 'o': [2.5224437548182577, 2.403869668326684, 2.331724920680597, 2.1639625135282956, 2.454974053653062, 2.308318277805076, 2.3530968045661287, 2.286998092213958, 2.7847380736981195, 2.738048052297312, 2.5567379167823328], 'ffn': [21.772078254393534, 20.97356846977793, 24.490923192635307, 22.38591549534384, 22.23684226219228, 28.049039800725716, 28.14707693025976, 27.911982702114507, 26.957520003958823, 26.445886941361714, 24.698214120477466]}, 'bws': {'qkv': [66.44444274902344, 66.11111450195312, 59.6363639831543, 61.272727966308594, 61.44444274902344, 69.33333587646484, 56.33333206176758, 60.66666793823242, 50.13333511352539, 48.66666793823242, 50.69230651855469], 'attn': [25.027027130126953, 42.053192138671875, 61.978946685791016, 66.008544921875, 71.04273223876953, 79.1292495727539, 75.88700866699219, 82.20744323730469, 78.30803680419922, 83.23605346679688, 82.31922912597656], 'o': [48.57143020629883, 51.875, 57.5, 54.22222137451172, 53.625, 54.55555725097656, 54.625, 51.125, 44.44444274902344, 45.3636360168457, 41.90909194946289], 'ffn': [75.3548355102539, 75.63636016845703, 64.60526275634766, 64.7662353515625, 68.30769348144531, 55.086021423339844, 54.739131927490234, 54.54255294799805, 53.04166793823242, 52.71428680419922, 51.53061294555664]}, 'comps': {'qkv': [31.22222137451172, 29.11111068725586, 25.454545974731445, 25.727272033691406, 30.0, 28.88888931274414, 23.08333396911621, 24.75, 40.400001525878906, 39.599998474121094, 40.38461685180664], 'attn': [1.2837837934494019, 1.2553191184997559, 1.24210524559021, 1.0, 1.0, 1.0, 1.073446273803711, 1.021276593208313, 1.2142857313156128, 1.0, 1.1038461923599243], 'o': [22.571428298950195, 22.125, 26.0, 22.44444465637207, 25.75, 22.88888931274414, 23.625, 22.25, 39.88888931274414, 37.45454406738281, 34.727272033691406], 'ffn': [33.74193572998047, 31.969696044921875, 27.526315689086914, 27.259740829467773, 32.81538391113281, 38.31182861328125, 38.510868072509766, 37.76595687866211, 43.97916793823242, 43.163265228271484, 42.65306091308594]}}
        # 0320, batch size in range(8, 89, 8), llama-2 7B
        batch_sizes = [8, 16, 24, 32, 40, 48, 56, 64, 72, 80]
        data = {'latency': {'qkv': [5.419803123492867, 5.523408487755453, 5.299656758704016, 5.500271648056024, 5.819963373071648, 5.2406728756384275, 5.240978389614342, 4.92941740027275, 6.494641734677711, 6.404776050923884], 'attn': [7.315108948796757, 15.279017182801397, 20.507322620340005, 26.525691303737215, 31.180794159439763, 37.20381671489642, 44.9274399423459, 43.32615613222995, 50.645163763101806, 55.30725427883068], 'o': [2.215920351275527, 2.2550974404737003, 2.298420644089574, 2.3141840570846983, 2.1476278123973054, 2.0878173953084627, 2.2494686120234637, 1.9176568923126556, 2.485703626858273, 2.430057889544515], 'ffn': [14.896876392575376, 15.17955492965602, 14.475445635344794, 15.38274959308744, 14.174292133953143, 14.17855629074749, 14.596455190145443, 13.396127732998345, 15.825816771344906, 15.437723336574852]}, 'bws': {'qkv': [74.26667022705078, 77.0, 73.53333282470703, 66.35294342041016, 58.38888931274414, 67.35294342041016, 70.26667022705078, 73.61111450195312, 57.875, 56.959999084472656], 'attn': [76.0, 76.0, 81.55931854248047, 84.76000213623047, 82.97916412353516, 82.46154022216797, 81.57664489746094, 90.7874984741211, 91.265625, 91.35185241699219], 'o': [47.0, 56.33333206176758, 45.66666793823242, 50.0, 46.83333206176758, 39.83333206176758, 48.875, 51.5, 48.77777862548828, 50.44444274902344], 'ffn': [72.97618865966797, 72.16666412353516, 75.375, 69.86666870117188, 69.33333587646484, 68.84444427490234, 70.28888702392578, 72.0999984741211, 63.655738830566406, 64.43333435058594]}, 'comps': {'qkv': [38.266666412353516, 40.13333511352539, 38.53333282470703, 34.0, 30.16666603088379, 34.35293960571289, 36.33333206176758, 33.05555725097656, 49.58333206176758, 48.08000183105469], 'attn': [0.800000011920929, 1.3571428060531616, 1.7627118825912476, 1.0800000429153442, 1.1354166269302368, 1.307692289352417, 1.0, 1.056249976158142, 1.125, 1.0694444179534912], 'o': [26.0, 30.0, 24.5, 26.5, 25.33333396911621, 21.83333396911621, 24.625, 23.25, 41.55555725097656, 42.11111068725586], 'ffn': [37.738094329833984, 37.14285659790039, 38.974998474121094, 35.71111297607422, 35.511112213134766, 34.911109924316406, 35.622222900390625, 32.13999938964844, 52.819671630859375, 53.31666564941406]}}
    else:
        batch_sizes, data = get_data(args)

    target_batch_sizes = [16, 32, 48, 64]
    scheme_order = ['qkv', 'attn', 'o', 'ffn']
    plot_kernel_util_small(target_batch_sizes, batch_sizes, data, scheme_order, 'comps', 'Comp. Util. (%)', f"{args.output_dir}/legend_decode_kernel_util.pdf")
    plot_kernel_util_small(target_batch_sizes, batch_sizes, data, scheme_order, 'comps', 'Comp. Util. (%)', f"{args.output_dir}/decode_kernel_comp_util.pdf")
    plot_kernel_mem_util_small(target_batch_sizes, batch_sizes, data, scheme_order, f"{args.output_dir}/decode_kernel_bw_util.pdf")

    if not args.no_crop_margin:
        output_path = crop_margins(
            f"{args.output_dir}/legend_decode_kernel_util.pdf", "-p4 10 -1800 10 10"
        )
        print(f"Croped to {output_path} successfully.")
        output_path = crop_margins(f"{args.output_dir}/decode_kernel_comp_util.pdf")
        print(f"Croped to {output_path} successfully.")
        output_path = crop_margins(f"{args.output_dir}/decode_kernel_bw_util.pdf")
        print(f"Croped to {output_path} successfully.")
