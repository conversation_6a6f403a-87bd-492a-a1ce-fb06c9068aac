import argparse
import matplotlib

from matplotlib import pyplot


matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 10
matplotlib.rcParams['axes.titlesize'] = 10
matplotlib.rcParams['axes.labelsize'] = 10
matplotlib.rcParams['xtick.labelsize'] = 10
matplotlib.rcParams['ytick.labelsize'] = 10
matplotlib.rcParams['legend.fontsize'] = 10


def plot_attn_latency(
    offload_seq_lens: list[int],
    local_attn_times: list[float],
    offload_attn_times: list[float],
    output_path: str = "untitled.pdf",
):
    pyplot.plot(
        offload_seq_lens,
        local_attn_times,
        marker="o",
        label="Local Attention Latency",
        color="blue",
    )
    pyplot.plot(
        offload_seq_lens,
        offload_attn_times,
        marker="o",
        label="Offloaded Attention Latency",
        color="red",
    )
    unified_attn_times = [
        max(local_time, offload_time + 50)
        for local_time, offload_time in zip(local_attn_times, offload_attn_times)
    ]
    pyplot.plot(
        offload_seq_lens,
        unified_attn_times,
        marker="o",
        label="Unified Attention Latency",
        color="green",
    )
    pyplot.xlabel("Offload Sequence Length")
    pyplot.ylabel("Attention Latency (us)")
    pyplot.savefig(output_path)


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--output-path", type=str, default="evaluation/plots/attn_latency_with_offload_len/attn_latency_with_offload_len.pdf"
    )
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    print(args)
    output_path: str = args.output_path
    local_seq_len = 1024
    offload_seq_lens = [512, 1024, 1536, 2048, 2560]
    local_attn_times = [586.28 for _ in range(len(offload_seq_lens))]  # ms
    offload_attn_times = [274.09, 491.69, 654.49, 882.43, 1009.42]  # ms

    plot_attn_latency(
        offload_seq_lens, local_attn_times, offload_attn_times, output_path
    )
