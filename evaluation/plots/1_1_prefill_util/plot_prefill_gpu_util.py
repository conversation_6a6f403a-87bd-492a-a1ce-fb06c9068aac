import argparse
import matplotlib
import matplotlib.pyplot as plt
import glob
import torch

from typing import Any, Optional

from evaluation.plots.nsys import utils, sqlite_parser
from evaluation.plots.nsys.parser import NsysParser
from evaluation.plots.nsys.utils import MetricNames
from evaluation.plots.utils.plot_utils import crop_margins

from dataclasses import dataclass
from matplotlib.ticker import MultipleLocator

matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.titlesize'] = 12
matplotlib.rcParams['axes.labelsize'] = 12
matplotlib.rcParams['xtick.labelsize'] = 12
matplotlib.rcParams['ytick.labelsize'] = 12
matplotlib.rcParams['legend.fontsize'] = 12

@dataclass
class PlotConfig:
    figsize = (2.8, 2.2)
    colors = ['#0000ED', '#BF0000']
    marker_style = ['o', 's', '^', 'v', 'D', '<', '>', 's', 'x', '*']
    markersize = 6
    markeredgewidth = 1.5

@dataclass
class TestReportKernelPoints_0107:
    layer_line_num: str = "llama.py:244(forward)"
    qkv_line_num: str = "linear.py:366(forward)"
    attn_line_num: str = "custom_ops.py:30(custom_decode_attn)"
    o_line_num: str = "linear.py:1066(forward)"
    ffn1_line_num: str = "linear.py:366(forward)"
    ffn2_line_num: str = "linear.py:1066(forward)"

class ReportKernelPoints:

    def __init__(self, layer: str, qkv: str, attn: str, o: str,
                 ffn1: str, ffn2: str):
        self.layer_line_num = layer
        self.qkv_line_num = qkv
        self.attn_line_num = attn
        self.o_line_num = o
        self.ffn1_line_num = ffn1
        self.ffn2_line_num = ffn2

class DBCursor:
    def __init__(self,
                 parser: NsysParser,
                 roi_start: Optional[int] = None,
                 roi_end: Optional[int] = None):
        self.parser = parser
        self.roi_start = roi_start
        self.roi_end = roi_end
    
    # The following 3 methods are copied from `Event`
    def get_gpu_metric(self, metric_name: str):
        return sqlite_parser.get_gpu_metric(
            self.parser.cur,
            self.parser.metrics_id[metric_name],
            (self.roi_start, self.roi_end),
        )

    def get_comp_power(self) -> tuple[torch.Tensor, torch.Tensor]:
        tensor_active, timestamp = self.get_gpu_metric(MetricNames.TENSOR_ACT_TP)
        comp_power = tensor_active
        return comp_power, timestamp

    def get_dram_bw(self) -> tuple[torch.Tensor, torch.Tensor]:
        dram_rd_bw, timestamp = self.get_gpu_metric(MetricNames.DRAM_RD_BW)
        dram_wt_bw, _ = self.get_gpu_metric(MetricNames.DRAM_WT_BW)
        assert timestamp.all() == _.all()
        dram_bw = dram_rd_bw + dram_wt_bw
        # print(dram_bw[1000:])
        return dram_bw, timestamp


def get_kernel_stats(profile_path: str,
                     kernel_points: ReportKernelPoints,
                     roi_start: Optional[int] = None,
                     roi_end: Optional[int] = None,
                     split_tag: str = ''):
    print(f"start processing {profile_path}...")
    p = NsysParser(profile_path)
    es = p.get_nvtx_events(kernel_points.layer_line_num, roi_start, roi_end)
    # skip 2 starting layers and 2 trailing layers
    assert len(es) > 30
    start_time = es[1].cuda_end_time
    end_time = es[-1].cuda_start_time
    cursor = DBCursor(p, start_time, end_time)
    filename = profile_path.rsplit(split_tag, 1)[-1]
    prompt_len = int(filename.split('_', 1)[0])
    # print('filename: ', filename, prompt_len)
    comp_power, _ = cursor.get_comp_power()
    dram_bw, _ = cursor.get_dram_bw()
    avg_comp_util = (torch.sum(comp_power)/comp_power.numel()).item()
    avg_bw_util = (torch.sum(dram_bw)/dram_bw.numel()).item()
    print('comp_power (%)', torch.sum(comp_power)/comp_power.numel())
    print('dram_bw (%) ', torch.sum(dram_bw)/dram_bw.numel())
    return prompt_len, avg_comp_util, avg_bw_util

def get_data(args):
    TestReportKernelPoints = TestReportKernelPoints_0107
    trace_info = ReportKernelPoints(
        layer=TestReportKernelPoints.layer_line_num,
        qkv=TestReportKernelPoints.qkv_line_num,
        attn=TestReportKernelPoints.attn_line_num,
        o=TestReportKernelPoints.o_line_num,
        ffn1=TestReportKernelPoints.ffn1_line_num,
        ffn2=TestReportKernelPoints.ffn2_line_num
    )
    results = []
    for rpath in glob.glob(f"{args.reports_dir}/*.sqlite"):
        prompt_len, avg_comp_util, avg_bw_util = get_kernel_stats(rpath, trace_info, split_tag='1_1_prefill_util_')
        results.append((prompt_len, avg_comp_util, avg_bw_util))
    sorted_results = sorted(results, key=lambda t: t[0])
    data = {'mem_util': {'x': [], 'y': []}, 'comp_util': {'x': [], 'y': []}}
    data['mem_util']['x'] = [x[0] for x in sorted_results]
    data['mem_util']['y'] = [x[2] for x in sorted_results]
    data['comp_util']['x'] = [x[0] for x in sorted_results]
    data['comp_util']['y'] = [x[1] for x in sorted_results]

    print(data)
    return data

scheme_to_label = {
    'mem_util': 'Mem BW',
    'comp_util': 'Compute',
}

def plot_gpu_util(data, scheme_order, save_path):
    fig, ax = plt.subplots(figsize=PlotConfig.figsize)

    plot_with_legend = 'legend' in save_path

    # for i, t in enumerate(data.items()):
    for i, scheme in enumerate(scheme_order):
        # scheme, scatters = t
        scatters = data[scheme]
        ax.plot(scatters['x'], scatters['y'],
            marker=PlotConfig.marker_style[i], color=PlotConfig.colors[i],
            markersize=PlotConfig.markersize, markerfacecolor='none',
            markeredgewidth=PlotConfig.markeredgewidth,
            label=scheme_to_label[scheme])

    ax.set_xlabel('Prompt length')
    ax.set_ylabel('Resource Util. (%)')

    # # https://matplotlib.org/3.5.0/gallery/ticks/major_minor_demo.html
    ax.xaxis.set_major_locator(MultipleLocator(1024))

    # https://stackabuse.com/how-to-set-axis-range-xlim-ylim-in-matplotlib/
    plt.xlim([120, 4400])
    plt.ylim([0, 100])

    # https://www.w3schools.com/python/matplotlib_grid.asp
    plt.grid(axis='y', color='lightgray', linestyle='--', linewidth=0.25)
    plt.grid(axis='x', color='lightgray', linestyle='--', linewidth=0.25)

    if plot_with_legend:
        # Set the legend
        # https://stackoverflow.com/questions/4700614/how-to-put-the-legend-outside-the-plot-in-matplotlib
        # https://matplotlib.org/stable/api/figure_api.html#matplotlib.figure.Figure
        fig.legend(loc='upper center', ncol=4, frameon=False)
        # fig.legend(loc='lower left', bbox_to_anchor=(0.15, 0.8, 0.8, 0.2), ncol=4, fontsize=9, frameon=False)
        fig.subplots_adjust(top=0.75)
    else:
        fig.legend(loc=(0.5, 0.45), fontsize=10, frameon=False)  # 设置背景

    # plt.show()
    plt.savefig(save_path, bbox_inches='tight')
    print('saved in ' + save_path)


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--reports-dir", type=str, default="evaluation/results/1_1_prefill_util")
    parser.add_argument("--use-existed-data", action="store_true")
    parser.add_argument("--no-crop-margin", action="store_true")
    parser.add_argument(
        "--output-path",
        type=str,
        default="evaluation/plots/1_1_prefill_util/prefill_resource_util.pdf",
    )
    args = parser.parse_args()
    return args

if __name__ == "__main__":
    args = parse_args()
    if args.use_existed_data:
        data = {'mem_util': {'x': [256, 512, 768, 1024, 1280, 1536, 1792, 2048], 'y': [22.328279495239258, 22.201526641845703, 28.92156410217285, 21.166322708129883, 25.885108947753906, 23.33470916748047, 25.213836669921875, 20.282390594482422]}, 'comp_util': {'x': [256, 512, 768, 1024, 1280, 1536, 1792, 2048], 'y': [38.07956314086914, 62.75852584838867, 67.30145263671875, 68.21307373046875, 73.141357421875, 73.04074096679688, 70.1557846069336, 75.56056213378906]}}
    else:
        data = get_data(args)
    target_prompt_lens = list(range(512, 4096+1, 512))
    data["mem_util"] = {
        "x": target_prompt_lens,
        "y": [data["mem_util"]["y"][i] for i in range(len(data["mem_util"]["y"])) if data["mem_util"]["x"][i] in target_prompt_lens]
    }
    data["comp_util"] = {
        "x": target_prompt_lens,
        "y": [data["comp_util"]["y"][i] for i in range(len(data["comp_util"]["y"])) if data["comp_util"]["x"][i] in target_prompt_lens]
    }
    scheme_order = ['comp_util', 'mem_util']
    plot_gpu_util(data, scheme_order, args.output_path)
    if not args.no_crop_margin:
        crop_path = crop_margins(args.output_path)
        print(f"Croped {args.output_path} to {crop_path} successfully.")
