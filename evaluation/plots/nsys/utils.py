import enum
import inspect
import pathlib

from typing import Callable


class MetricNames(enum.StrEnum):
    GPC_CLK_FRQ = "GPC Clock Frequency [MHz]"
    SYS_CLK_FRQ = "SYS Clock Frequency [MHz]"
    GR_ACT = "GR Active [Throughput %]"
    SYNC_COMP = "Sync Compute in Flight [Throughput %]"
    ASYNC_COMP = "Async Compute in Flight [Throughput %]"
    SM_ACT = "SM Active [Throughput %]"
    SM_ISSUE = "SM Issue [Throughput %]"
    TENSOR_ACT_TP = "Tensor Active [Throughput %]"
    VTG_WARP_TP = "Vertex/Tess/Geometry Warps in Flight [Throughput %]"
    VTG_WARP_AVG = "Vertex/Tess/Geometry Warps in Flight [Avg]"
    VTG_WARP_WPC = "Vertex/Tess/Geometry Warps in Flight [Avg Warps per Cycle]"
    PIX_WARP_TP = "Pixel Warps in Flight [Throughput %]"
    PIX_WARP_AVG = "Pixel Warps in Flight [Avg]"
    PIX_WARP_WPC = "Pixel Warps in Flight [Avg Warps per Cycle]"
    COMP_WARP_TP = "Compute Warps in Flight [Throughput %]"
    COMP_WARP_AVG = "Compute Warps in Flight [Avg]"
    COMP_WARP_WPC = "Compute Warps in Flight [Avg Warps per Cycle]"
    UNALLOC_WARP_ACT_SM_TP = "Unallocated Warps in Active SMs [Throughput %]"
    UNALLOC_WARP_ACT_SM_AVG = "Unallocated Warps in Active SMs [Avg]"
    UNALLOC_WARP_ACT_SM_WPC = "Unallocated Warps in Active SMs [Avg Warps per Cycle]"
    DRAM_RD_BW = "DRAM Read Bandwidth [Throughput %]"
    DRAM_WT_BW = "DRAM Write Bandwidth [Throughput %]"
    PCIE_RX_TP = "PCIe RX Throughput [Throughput %]"
    PCIE_TX_TP = "PCIe TX Throughput [Throughput %]"
    PCIE_RD_BAR = "PCIe Read Requests to BAR1 [Requests]"
    PCIE_WT_BAR = "PCIe Write Requests to BAR1 [Requests]"


def nvtx(f: Callable) -> str:
    filename = pathlib.Path(inspect.getfile(f)).name
    lineno = str(inspect.getsourcelines(f)[1])
    func_name = f.__name__
    return f"{filename}:{lineno}({func_name})"


class DeviceMetaData:
    def __init__(self, peak_fp16_tflops: float, memory_bandwidth: float, sm_count: int):
        self.PEAK_FP16_TFLOPS = peak_fp16_tflops
        self.MEMORY_BANDWIDTH = memory_bandwidth
        self.SM_COUNT = sm_count


# ref: https://images.nvidia.cn/aem-dam/Solutions/geforce/ada/nvidia-ada-gpu-architecture.pdf
GTX4090_META_DATA = DeviceMetaData(
    peak_fp16_tflops=330.3,
    memory_bandwidth=1008,
    sm_count=128,
)
# ref: https://www.nvidia.com/content/dam/en-zz/Solutions/Data-Center/a100/pdf/nvidia-a100-datasheet-us-nvidia-1758950-r4-web.pdf
A100_40GB_META_DATA = DeviceMetaData(
    peak_fp16_tflops=312,
    memory_bandwidth=1555,
    sm_count=108,
)
# ref: https://www.nvidia.com/en-us/data-center/a100/
A100_80GB_META_DATA = DeviceMetaData(
    peak_fp16_tflops=312,
    memory_bandwidth=2039,
    sm_count=108,
)

_devs_meta_data = {
    "NVIDIA GeForce RTX 4090": GTX4090_META_DATA,
    "NVIDIA A100-SXM4-80GB": A100_80GB_META_DATA,
    "NVIDIA A100-SXM4-40GB": A100_40GB_META_DATA,
}


def get_dev_meta_data(name: str) -> DeviceMetaData:
    assert name in _devs_meta_data.keys()
    return _devs_meta_data[name]
