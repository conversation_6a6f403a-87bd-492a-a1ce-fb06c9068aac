import argparse
import matplotlib
import numpy

from dataclasses import dataclass
from matplotlib import pyplot
from matplotlib.ticker import MultipleLocator

from evaluation.plots.utils.plot_utils import (
    CommonColors, crop_margins
)
from evaluation.plots.nsys.parser import N<PERSON>sParser
from evaluation.plots.utils.plot_utils import calc_mean_with_process


matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 10
matplotlib.rcParams['axes.titlesize'] = 10
matplotlib.rcParams['axes.labelsize'] = 10
matplotlib.rcParams['xtick.labelsize'] = 10
matplotlib.rcParams['ytick.labelsize'] = 10
matplotlib.rcParams['legend.fontsize'] = 10


def get_transfer_latency(
    nvtx_name: str, sender_parser: <PERSON><PERSON><PERSON><PERSON>arser, recver_parser: NsysParser
):
    send_events = sender_parser.get_nvtx_events(nvtx_name)
    recv_events = recver_parser.get_nvtx_events(nvtx_name)
    assert len(send_events) == len(recv_events)
    transfer_latencies = [min(
        send_event.cuda_duration, recv_event.cuda_duration
    ) for send_event, recv_event in zip(send_events, recv_events)]
    transfer_latency = calc_mean_with_process(transfer_latencies)
    return transfer_latency


@dataclass
class FusedPlotConfig:
    figsize = (2.8, 1.9)
    # colors = ['#0000ED', '#EAAABB', '#D45555', '#BF0000']
    # colors = ['white', '#72ACD1', '#408EC1', '#07428E', '#BF0000']
    colors = [CommonColors.gradient_blues[2], CommonColors.gradient_reds[2]]
    # colors = CommonColors.gradient_reds
    marker_style = ['o', 's', '^', 'v', 'D', '<', '>', 's', 'x', '*']
    # hatch_style = [None, '///']
    n_groups = 2
    bar_width = 0.15
    bar_padding = 1.4
    markersize = 6
    markeredgewidth = 0.5
    grid_linewidth = 0.3


scheme_to_label = {
    "cross_node": "Cross-node",
    "within_node": "Inter-node",
}

def plot_latency_and_bandwidth(
    schemes: list[str],
    bandwidths: dict[tuple[str, str], list[float]],
    latencies: dict[tuple[str, str], list[float]],
    sizes: list[float],
    output_path: str,
):
    fig, ax1 = pyplot.subplots(figsize=FusedPlotConfig.figsize)
    ax2 = ax1.twinx()

    print(sizes)
    print(bandwidths)
    print(latencies)
    n_groups = len(bandwidths[schemes[0]])
    # x_ticks = ['Llama-2 7B', 'Llama-2 13B']
    x_ticks = sizes
    print(n_groups, x_ticks)
    assert n_groups == len(x_ticks)

    x = numpy.arange(n_groups) # the label locations
    width = FusedPlotConfig.bar_width  # the width of the bars
    offsets = numpy.arange(len(schemes)) * width - width * (len(schemes) - 1) / 2
    offsets = offsets * FusedPlotConfig.bar_padding # add spaces for bars in a group

    for i, scheme in enumerate(schemes):
        tag = scheme_to_label[scheme]
        target_value = [x for x in bandwidths[scheme]]
        ax1.bar(x + offsets[i], target_value, width, label=tag,
                # hatch=FusedPlotConfig.hatch_style[i],
                color=FusedPlotConfig.colors[i], edgecolor='black', linewidth=1)

    for i in range(len(sizes)):
        x_values = [i + x for x in offsets]
        y_values = [latencies[scheme][i] for scheme in schemes]
        label = 'Tran. Latency' if i == 0 else None
        ax2.plot(x_values, y_values, marker=FusedPlotConfig.marker_style[0],
                 color='black', markersize=FusedPlotConfig.markersize, label=label,
                 markerfacecolor='white', markeredgewidth=FusedPlotConfig.markeredgewidth)

    pyplot.xticks(x, x_ticks)

    ax1.set_xlabel('Transfer Batch Size')
    ax1.set_ylabel('Bandwidth (GB/s)')
    ax2.set_ylabel('Transfer Latency(us)')
    # ax2.yaxis.label.set_color('red')
    # ax1.set_ylim([0, 2000])
    # ax2.set_ylim([0, 100])
    ax2.yaxis.set_major_locator(MultipleLocator(40))


    # pyplot.tight_layout()
    # https://www.geeksforgeeks.org/how-to-place-legend-outside-of-the-plot-in-matplotlib/
    # fig.legend(bbox_to_anchor=(1.4, 0.8), frameon=False)

    # handlelength：控制图例中 marker 的宽度，默认值通常是 2
    # handletextpad：控制 marker 和图例文本之间的间距。
    # labelspacing：控制图例项之间的垂直间距。
    # fig.legend(loc=(0.25, 0.69), fontsize=10, frameon=False, handlelength=1)

    ax2.legend(loc=(0.02, 0.81), fontsize=9, frameon=False)

    # fig.legend(ncol=2, loc=(0.17, 0.85), fontsize=10, frameon=False)
    # fig.subplots_adjust(top=0.8)

    ax1.legend(ncol=4, loc=(0, 1.05), fontsize=10, frameon=False, handlelength=1)
    fig.subplots_adjust(top=0.8)


    ax1.grid(axis='y', color='lightgray', linestyle='--')
    ax1.set_axisbelow(True)
    # ax2.set_axisbelow(True)

    pyplot.savefig(output_path, bbox_inches='tight')
    output_path = crop_margins(output_path)
    print(f"Croped to {output_path} successfully.")


def print_transfer_times(lats0: list[float], lats1: list[float]):
    times = [l0 / l1 for l0, l1 in zip(lats0, lats1)]
    print(f"compared with other: {times}")
    num_layers = 32
    total_transfer_times = [l * num_layers for l in lats0]
    print(f"total transfer times: {total_transfer_times}")


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--batches", nargs="+", type=int, default=[8, 16, 24, 32, 40]
    )
    parser.add_argument("--op", type=str, default="scatter")
    parser.add_argument(
        "--profile-dir", type=str, default="evaluation/results/transfer_latency"
    )
    parser.add_argument(
        "--output-path", type=str, default="evaluation/plots/transfer_latency/transfer_latency.pdf"
    )
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    print(args)
    batches: list[int] = args.batches
    op: str = args.op
    profile_dir: str = args.profile_dir
    output_path: str = args.output_path

    types = ["cross_node", "within_node"]
    configs = [f"{batch}_{op}" for batch in batches]
    profiles_paths = {
        (typ, config, idx): f"{profile_dir}/{typ}/{config}_{idx}.sqlite"
        for typ in types
        for config in configs
        for idx in range(2)
    }
    nsys_parsers = {key: NsysParser(path) for key, path in profiles_paths.items()}
    latencies = {
        typ: [
            get_transfer_latency(
                op, nsys_parsers[(typ, config, 0)], nsys_parsers[(typ, config, 1)]
            ) / 1e3
            for config in configs
        ]
        for typ in types
    }  # us
    sizes = [batch * 4096 * 3 * 2 / 1e3 for batch in batches]  # KB
    sizes = [int(size) for size in sizes]
    bandwidths = {
        typ: [
            size / latency
            for size, latency in zip(sizes, latencies[typ])
        ]
        for typ in types
    }
    plot_latency_and_bandwidth(
        ["cross_node", "within_node"],
        bandwidths,
        latencies,
        batches,
        output_path,
    )
    print_transfer_times(latencies["cross_node"], latencies["within_node"])
