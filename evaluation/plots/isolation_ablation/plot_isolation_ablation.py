import argparse
import itertools
import statistics
import matplotlib
import numpy

from matplotlib import pyplot
from matplotlib.ticker import MultipleLocator
from dataclasses import dataclass

from evaluation.utils.output_parser import parse_gpu_time
from evaluation.plots.dataset_performance.utils import (
    get_ttft_for_config,
    get_thpt_for_config,
    get_tpot_for_config
)

from evaluation.plots.utils.plot_utils import (
    CommonColors, SYS_NAME
)

matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 10
matplotlib.rcParams['axes.titlesize'] = 10
matplotlib.rcParams['axes.labelsize'] = 10
matplotlib.rcParams['xtick.labelsize'] = 10
matplotlib.rcParams['ytick.labelsize'] = 10
matplotlib.rcParams['legend.fontsize'] = 10

scheme_to_label = {
    'baseline': 'w/o Isolation',
    'ours': SYS_NAME,
}

# https://www.codeeeee.com/color/picker.html
@dataclass
class PlotConfig:
    figsize = (1.5, 1.8)
    # colors = ['#0000ED', '#EAAABB', '#D45555', '#BF0000']
    # colors = ['white', '#72ACD1', '#408EC1', '#07428E', '#BF0000']
    colors = ['white', CommonColors.gradient_reds[2]]
    marker_style = ['o', 's', '^', 'v', 'D', '<', '>', 's', 'x', '*']
    n_groups = 2
    bar_width = 0.26
    bar_padding = 1.4

def plot_isolation_ablation_on_tpot(
    schemes: list[str],
    data: dict[str, list[float]],
    qps_list: list[float],
    output_path: str,
):
    fig, ax1 = pyplot.subplots(figsize=PlotConfig.figsize)
    # ax2 = ax1.twinx()

    n_groups = len(data[schemes[0]])
    # x_ticks = ['Llama-2 7B', 'Llama-2 13B']
    x_ticks = qps_list
    assert n_groups == len(x_ticks)

    x = numpy.arange(n_groups) # the label locations
    width = PlotConfig.bar_width  # the width of the bars
    offsets = numpy.arange(len(schemes)) * width - width * (len(schemes) - 1) / 2
    offsets = offsets * PlotConfig.bar_padding # add spaces for bars in a group

    plot_with_legend = 'legend' in output_path
    group_labels = []

    for i in range(len(schemes)):
        scheme = schemes[i]
        tag = scheme_to_label[scheme]
        group_labels.append(tag)
        print(scheme, tag)
        target_value = data[scheme]
        ax1.bar(x + offsets[i], target_value, width, label=tag, 
                color=PlotConfig.colors[i], edgecolor='black', linewidth=1)

    pyplot.xticks(x, x_ticks)

    ax1.set_xlabel('Req rate (req/s)')
    # ax1.set_ylabel('Throughput (tokens/s)')
    ax1.set_ylabel('Mean TPOT (ms)')

    # ax2.set_ylabel('Execute Time Ratio (%)', color='red')
    # ax2.yaxis.label.set_color('red')

    # ax1.set_ylim([600, 1500])
    ax1.set_ylim([0, 80])

    # ax1.yaxis.set_major_locator(MultipleLocator(300))
    ax1.yaxis.set_major_locator(MultipleLocator(20))

    # pyplot.tight_layout()
    # https://www.geeksforgeeks.org/how-to-place-legend-outside-of-the-plot-in-matplotlib/
    # fig.legend(bbox_to_anchor=(1.4, 0.8), frameon=False)

    # handlelength：控制图例中 marker 的宽度，默认值通常是 2
    # handletextpad：控制 marker 和图例文本之间的间距。
    # labelspacing：控制图例项之间的垂直间距。
    # fig.legend(loc=(0.26, 0.69), fontsize=10, frameon=False, handlelength=1)
    if plot_with_legend:
        fig.legend(group_labels, ncol=2, loc='upper center', frameon=False, handlelength=1)
        fig.subplots_adjust(top=0.7)

    pyplot.grid(axis='y', color='lightgray', linestyle='--')
    ax1.set_axisbelow(True)
    # ax2.set_axisbelow(True)

    pyplot.savefig(output_path, bbox_inches='tight')
    pyplot.clf()
    pyplot.cla()

def plot_isolation_ablation_on_ttft(
    schemes: list[str],
    data: dict[str, list[float]],
    qps_list: list[float],
    output_path: str,
):
    fig, ax1 = pyplot.subplots(figsize=PlotConfig.figsize)
    # ax2 = ax1.twinx()

    n_groups = len(data[schemes[0]])
    # x_ticks = ['Llama-2 7B', 'Llama-2 13B']
    x_ticks = qps_list
    assert n_groups == len(x_ticks)

    x = numpy.arange(n_groups) # the label locations
    width = PlotConfig.bar_width  # the width of the bars
    offsets = numpy.arange(len(schemes)) * width - width * (len(schemes) - 1) / 2
    offsets = offsets * PlotConfig.bar_padding # add spaces for bars in a group

    for i in range(len(schemes)):
        scheme = schemes[i]
        tag = scheme_to_label[scheme]
        print(scheme, tag)
        # target_value_ms = data[scheme]
        target_value_s = [ttft_ms/1000 for ttft_ms in data[scheme]]
        ax1.bar(x + offsets[i], target_value_s, width, label=tag, 
                color=PlotConfig.colors[i], edgecolor='black', linewidth=1)

    pyplot.xticks(x, x_ticks)

    ax1.set_xlabel('Req rate (req/s)')
    ax1.set_ylabel('Mean TTFT (s)')

    ax1.set_ylim([0, 3])

    ax1.yaxis.set_major_locator(MultipleLocator(1))

    # pyplot.tight_layout()
    # https://www.geeksforgeeks.org/how-to-place-legend-outside-of-the-plot-in-matplotlib/
    # fig.legend(bbox_to_anchor=(1.4, 0.8), frameon=False)

    # handlelength：控制图例中 marker 的宽度，默认值通常是 2
    # handletextpad：控制 marker 和图例文本之间的间距。
    # labelspacing：控制图例项之间的垂直间距。
    # fig.legend(loc=(0.26, 0.69), fontsize=10, frameon=False, handlelength=1)

    pyplot.grid(axis='y', color='lightgray', linestyle='--')
    ax1.set_axisbelow(True)
    # ax2.set_axisbelow(True)

    pyplot.savefig(output_path, bbox_inches='tight')


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--dataset", type=str, default="sharegpt")
    parser.add_argument(
        "--qps",
        nargs="+",
        type=str,
        default=["2", "3", "4"],
    )
    parser.add_argument("--numreqs", type=int, default=250)
    parser.add_argument(
        "--tpot-output-path",
        type=str,
        default="evaluation/plots/isolation_ablation/isolation_ablation_tpot.pdf",
    )
    parser.add_argument(
        "--legend-output-path",
        type=str,
        default="evaluation/plots/isolation_ablation/isolation_ablation_legend.pdf",
    )
    parser.add_argument(
        "--ttft-output-path",
        type=str,
        default="evaluation/plots/isolation_ablation/isolation_ablation_ttft.pdf",
    )
    parser.add_argument("--baseline-dir", type=str, default="outputs/e2e_perf/offload/no_iso/ratio_0.7/meta-llama/Llama-2-7b-hf")
    parser.add_argument("--offload-dir", type=str, default="outputs/e2e_perf/offload/ratio_0.7")
    parser.add_argument("--num-execs", type=int, default=3)
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    configs = [f"{args.dataset}-qps_{qps}-numreqs_{args.numreqs}" for qps in args.qps]
    qps_list = [int(qps) for qps in args.qps]
    schemes = ['baseline', 'ours']
    thpt_data = {
        'baseline': [
            get_thpt_for_config(config, args.baseline_dir, args.num_execs)
            for config in configs
        ],
        'ours': [
            get_thpt_for_config(config, args.offload_dir, args.num_execs)
            for config in configs
        ]
    }
    print(f'{thpt_data=}')
    ttft_data = {
        'baseline': [
            get_ttft_for_config(config, args.baseline_dir, args.num_execs)
            for config in configs
        ],
        'ours': [
            get_ttft_for_config(config, args.offload_dir, args.num_execs)
            for config in configs
        ]
    }
    print(f'{ttft_data=}')
    ttft_benefits = [
        (ttft_data['baseline'][i] - ttft_data['ours'][i]) / ttft_data['baseline'][i]
        for i in range(len(qps_list))
    ]
    print(f'{ttft_benefits=}')
    tpot_data = {
        'baseline': [
            get_tpot_for_config(config, args.baseline_dir, args.num_execs)
            for config in configs
        ],
        'ours': [
            get_tpot_for_config(config, args.offload_dir, args.num_execs)
            for config in configs
        ]
    }
    print(f'{tpot_data=}')
    
    tpot_benefits = [
        (tpot_data['baseline'][i] - tpot_data['ours'][i]) / tpot_data['baseline'][i]
        for i in range(len(qps_list))
    ]
    print(f'{tpot_benefits=}')
    # data = {
    #     'baseline': [29.48, 52.68, 66.03],
    #     'ours': [22.40, 30.18, 47.68]
    # }
    print(f'{schemes=}')
    plot_isolation_ablation_on_tpot(
        schemes, tpot_data, qps_list, args.tpot_output_path
    )
    plot_isolation_ablation_on_tpot(
        schemes, tpot_data, qps_list, args.legend_output_path
    )
    plot_isolation_ablation_on_ttft(
        schemes, ttft_data, qps_list, args.ttft_output_path
    )
