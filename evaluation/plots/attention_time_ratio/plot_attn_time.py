import argparse
import os
import itertools
import matplotlib.pyplot as plt
import numpy
import pathlib
import pprint
import glob
import torch
import logging

from typing import Any, Optional, List
from evaluation.plots.nsys import utils, sqlite_parser
from evaluation.plots.nsys.parser import NsysParser, Event
from evaluation.plots.nsys.utils import MetricNames

from dataclasses import dataclass
from matplotlib.ticker import MultipleLocator

import matplotlib
matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.titlesize'] = 12
matplotlib.rcParams['axes.labelsize'] = 12
matplotlib.rcParams['xtick.labelsize'] = 12
matplotlib.rcParams['ytick.labelsize'] = 12
matplotlib.rcParams['legend.fontsize'] = 12

scheme_to_label = {
    'qkv': 'QKV proj.',
    'attn': 'Attention',
    'o': 'O proj.',
    'ffn': 'FFN',
}


def process_data(target_batch_sizes, batch_sizes, data):
    total = [
        sum([data['latency'][kernel][x] for kernel in scheme_to_label.keys()])
        for x in range(len(batch_sizes))
    ]
    workload_idxs = [batch_sizes.index(x) for x in target_batch_sizes]
    attn = [data['latency']['attn'][x]/total[x] * 100 for x in workload_idxs]
    others = [100 - x for x in attn]
    return [attn, others]

def plot_attention_time(batch_sizes, data, save_path):
    print(f'{batch_sizes=}')
    print(f'{data=}')

    fig, ax = plt.subplots(figsize=(2.8, 2))

    colors = ['#07428E', 'white']
    bar_width = 0.50
    bar_padding = 1.4

    x = numpy.arange(len(batch_sizes))
    plt.xticks(x, batch_sizes)

    ax.bar(x, data[0], width=bar_width, color=colors[0], label='Attention', edgecolor='black', linewidth=1)
    ax.bar(x, data[1], width=bar_width, color=colors[1], bottom=data[0], hatch='///', label='Others', edgecolor='black', linewidth=1)

    ax.set_xlabel('Batch size')
    ax.set_ylabel('Execute time ratio (%)')

    ax.set_ylim([0, 100])

    # fig.legend(bbox_to_anchor=(1.4, 0.8), fontsize=10, frameon=False, handlelength=1)
    # fig.legend(ncol=2, loc='upper center', frameon=False)
    fig.legend(ncol=2, loc=(0.17, 0.85), fontsize=11, frameon=False)
    fig.subplots_adjust(top=0.8)

    plt.grid(axis='y', color='lightgray', linestyle='--')
    ax.set_axisbelow(True)

    plt.savefig(save_path, bbox_inches='tight')
    logging.info('saved in ' + save_path)

if __name__ == "__main__":
    # # 0114, batch size 8~88, from 3_2_decode_kernel
    # batch_sizes = [8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88]
    # data = {'latency': {'qkv': [3.311983571132207, 3.19740500463392, 3.2495328522503995, 2.9970203162310463, 3.3671214284266053, 3.2901849519683535, 3.677611611531923, 3.2869734680548435, 3.801374160629322, 3.6971453799554044, 3.5556213565281762], 'attn': [26.750998990865437, 29.81350822254355, 29.859818624405804, 34.044997657871996, 41.58076523535382, 45.007241905977544, 54.80386520332362, 56.686983552234274, 62.28762667624659, 63.31640548363572, 65.53436352981093], 'o': [2.5224437548182577, 2.403869668326684, 2.331724920680597, 2.1639625135282956, 2.454974053653062, 2.308318277805076, 2.3530968045661287, 2.286998092213958, 2.7847380736981195, 2.738048052297312, 2.5567379167823328], 'ffn': [21.772078254393534, 20.97356846977793, 24.490923192635307, 22.38591549534384, 22.23684226219228, 28.049039800725716, 28.14707693025976, 27.911982702114507, 26.957520003958823, 26.445886941361714, 24.698214120477466]}, 'bws': {'qkv': [66.44444274902344, 66.11111450195312, 59.6363639831543, 61.272727966308594, 61.44444274902344, 69.33333587646484, 56.33333206176758, 60.66666793823242, 50.13333511352539, 48.66666793823242, 50.69230651855469], 'attn': [25.027027130126953, 42.053192138671875, 61.978946685791016, 66.008544921875, 71.04273223876953, 79.1292495727539, 75.88700866699219, 82.20744323730469, 78.30803680419922, 83.23605346679688, 82.31922912597656], 'o': [48.57143020629883, 51.875, 57.5, 54.22222137451172, 53.625, 54.55555725097656, 54.625, 51.125, 44.44444274902344, 45.3636360168457, 41.90909194946289], 'ffn': [75.3548355102539, 75.63636016845703, 64.60526275634766, 64.7662353515625, 68.30769348144531, 55.086021423339844, 54.739131927490234, 54.54255294799805, 53.04166793823242, 52.71428680419922, 51.53061294555664]}, 'comps': {'qkv': [31.22222137451172, 29.11111068725586, 25.454545974731445, 25.727272033691406, 30.0, 28.88888931274414, 23.08333396911621, 24.75, 40.400001525878906, 39.599998474121094, 40.38461685180664], 'attn': [1.2837837934494019, 1.2553191184997559, 1.24210524559021, 1.0, 1.0, 1.0, 1.073446273803711, 1.021276593208313, 1.2142857313156128, 1.0, 1.1038461923599243], 'o': [22.571428298950195, 22.125, 26.0, 22.44444465637207, 25.75, 22.88888931274414, 23.625, 22.25, 39.88888931274414, 37.45454406738281, 34.727272033691406], 'ffn': [33.74193572998047, 31.969696044921875, 27.526315689086914, 27.259740829467773, 32.81538391113281, 38.31182861328125, 38.510868072509766, 37.76595687866211, 43.97916793823242, 43.163265228271484, 42.65306091308594]}}

    # 0320, batch size 8~80, llama-2 7B, seq_len=1k, from 3_2_decode_kernel
    batch_sizes = [8, 16, 24, 32, 40, 48, 56, 64, 72, 80]
    data = {'latency': {'qkv': [5.419803123492867, 5.523408487755453, 5.299656758704016, 5.500271648056024, 5.819963373071648, 5.2406728756384275, 5.240978389614342, 4.92941740027275, 6.494641734677711, 6.404776050923884], 'attn': [7.315108948796757, 15.279017182801397, 20.507322620340005, 26.525691303737215, 31.180794159439763, 37.20381671489642, 44.9274399423459, 43.32615613222995, 50.645163763101806, 55.30725427883068], 'o': [2.215920351275527, 2.2550974404737003, 2.298420644089574, 2.3141840570846983, 2.1476278123973054, 2.0878173953084627, 2.2494686120234637, 1.9176568923126556, 2.485703626858273, 2.430057889544515], 'ffn': [14.896876392575376, 15.17955492965602, 14.475445635344794, 15.38274959308744, 14.174292133953143, 14.17855629074749, 14.596455190145443, 13.396127732998345, 15.825816771344906, 15.437723336574852]}, 'bws': {'qkv': [74.26667022705078, 77.0, 73.53333282470703, 66.35294342041016, 58.38888931274414, 67.35294342041016, 70.26667022705078, 73.61111450195312, 57.875, 56.959999084472656], 'attn': [76.0, 76.0, 81.55931854248047, 84.76000213623047, 82.97916412353516, 82.46154022216797, 81.57664489746094, 90.7874984741211, 91.265625, 91.35185241699219], 'o': [47.0, 56.33333206176758, 45.66666793823242, 50.0, 46.83333206176758, 39.83333206176758, 48.875, 51.5, 48.77777862548828, 50.44444274902344], 'ffn': [72.97618865966797, 72.16666412353516, 75.375, 69.86666870117188, 69.33333587646484, 68.84444427490234, 70.28888702392578, 72.0999984741211, 63.655738830566406, 64.43333435058594]}, 'comps': {'qkv': [38.266666412353516, 40.13333511352539, 38.53333282470703, 34.0, 30.16666603088379, 34.35293960571289, 36.33333206176758, 33.05555725097656, 49.58333206176758, 48.08000183105469], 'attn': [0.800000011920929, 1.3571428060531616, 1.7627118825912476, 1.0800000429153442, 1.1354166269302368, 1.307692289352417, 1.0, 1.056249976158142, 1.125, 1.0694444179534912], 'o': [26.0, 30.0, 24.5, 26.5, 25.33333396911621, 21.83333396911621, 24.625, 23.25, 41.55555725097656, 42.11111068725586], 'ffn': [37.738094329833984, 37.14285659790039, 38.974998474121094, 35.71111297607422, 35.511112213134766, 34.911109924316406, 35.622222900390625, 32.13999938964844, 52.819671630859375, 53.31666564941406]}}

    target_batch_sizes = [8, 16, 32, 48, 64, 80]
    # target_batch_sizes = [8, 16, 24, 32, 40, 48, 56, 64, 72, 80]
    scheme_order = ['qkv', 'attn', 'o', 'ffn']

    data = process_data(target_batch_sizes, batch_sizes, data)
    print(f'{data=}')
    
    plot_attention_time(target_batch_sizes, data, 'evaluation/plots/attention_time_ratio/attn_time_ratio.pdf')
