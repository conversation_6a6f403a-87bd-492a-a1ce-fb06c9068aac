import argparse
import glob
import statistics
import matplotlib
import numpy
from dataclasses import dataclass

from matplotlib import pyplot
from matplotlib.ticker import MultipleLocator


from evaluation.plots.dataset_performance.utils import (
    PlotConfig,
    get_ttft_for_config,
    get_tpot_for_config,
    get_thpt_for_config,
    get_p99_tpot_for_config,
)
from evaluation.plots.utils.plot_utils import (
    SYS_NAME, CommonColors, crop_margins
)

matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 10
matplotlib.rcParams['axes.titlesize'] = 10
matplotlib.rcParams['axes.labelsize'] = 10
matplotlib.rcParams['xtick.labelsize'] = 10
matplotlib.rcParams['ytick.labelsize'] = 10
matplotlib.rcParams['legend.fontsize'] = 10

scheme_to_label = {
    'baseline': 'vLLM',
    '0.5': '50%',
    '0.6': '60%',
    '0.7': '70%',
}

@dataclass
class FusedPlotConfig:
    figsize = (2.8, 1.9)
    # colors = ['#0000ED', '#EAAABB', '#D45555', '#BF0000']
    # colors = ['white', '#72ACD1', '#408EC1', '#07428E', '#BF0000']
    # colors = [CommonColors.gradient_blues[2], CommonColors.gradient_reds[2]]
    colors = [CommonColors.gradient_blues[2]] + CommonColors.gradient_reds[1:]
    # colors = CommonColors.gradient_reds
    marker_style = ['o', 's', '^', 'v', 'D', '<', '>', 's', 'x', '*']
    # hatch_style = [None, '///']
    n_groups = 2
    bar_width = 0.15
    bar_padding = 1.4
    markersize = 6
    markeredgewidth = 0.5
    grid_linewidth = 0.3


def plot_ttft(
    qps_list: list[float],
    offload_ttfts: dict[float, list[float]],
    disagg_ttfts: list[float],
    output_path: str,
):
    # fig, ax = pyplot.subplots(figsize=(3.2, 2)) # 1/3 column
    fig, ax = pyplot.subplots(figsize=PlotConfig.figsize) # 1/4 column

    disagg_ttfts_s = [t/1000 for t in disagg_ttfts]
    offload_ratio, offload_ttft = list(offload_ttfts.items())[0]
    offload_ttft_s = [t/1000 for t in offload_ttft]

    ax.plot(qps_list, disagg_ttfts_s, marker=PlotConfig.marker_style[0],
            color=PlotConfig.colors[0], markersize=PlotConfig.markersize,
            markerfacecolor='none', markeredgewidth=PlotConfig.markeredgewidth,
            label="vLLM")
    ax.plot(qps_list, offload_ttft_s, marker=PlotConfig.marker_style[1],
            color=PlotConfig.colors[1], markersize=PlotConfig.markersize,
            markerfacecolor='none', markeredgewidth=PlotConfig.markeredgewidth,
            label=SYS_NAME)

    ax.xaxis.set_major_locator(MultipleLocator(1))
    ax.yaxis.set_major_locator(MultipleLocator(5))

    pyplot.grid(axis='y', color='lightgray', linestyle='--', linewidth=PlotConfig.grid_linewidth)
    pyplot.grid(axis='x', color='lightgray', linestyle='--', linewidth=PlotConfig.grid_linewidth)


    # for idx, (offload_ratio, offload_ttft) in enumerate(offload_ttfts.items()):
    #     pyplot.plot(
    #         qps_list, offload_ttft, color=PlotConfig.colors[idx+1], label=f"offload (ratio={offload_ratio})"
    #     )
    pyplot.xlabel("Req rate (req/s)")
    pyplot.ylabel("Mean TTFT (s)")
    pyplot.tight_layout()
    pyplot.legend(frameon=False)
    # fig.legend(loc=(0.50, 0.56), frameon=False)  # 设置背景
    pyplot.savefig(output_path)


def plot_tpot(
    qps_list: list[float],
    offload_tpots: dict[float, list[float]],
    disagg_tpots: list[float],
    output_path: str,
):
    # fig, ax = pyplot.subplots(figsize=(3.2, 2)) # 1/3 column
    fig, ax = pyplot.subplots(figsize=PlotConfig.figsize) # 1/4 column

    offload_ratio, offload_tpot = list(offload_tpots.items())[0]

    ax.plot(qps_list, disagg_tpots, marker=PlotConfig.marker_style[0],
            color=PlotConfig.colors[0], markersize=PlotConfig.markersize,
            markerfacecolor='none', markeredgewidth=PlotConfig.markeredgewidth,
            label="vLLM")
    ax.plot(qps_list, offload_tpot, marker=PlotConfig.marker_style[1],
            color=PlotConfig.colors[1], markersize=PlotConfig.markersize,
            markerfacecolor='none', markeredgewidth=PlotConfig.markeredgewidth,
            label=SYS_NAME)

    ax.xaxis.set_major_locator(MultipleLocator(1))
    ax.yaxis.set_major_locator(MultipleLocator(10))

    pyplot.grid(axis='y', color='lightgray', linestyle='--', linewidth=PlotConfig.grid_linewidth)
    pyplot.grid(axis='x', color='lightgray', linestyle='--', linewidth=PlotConfig.grid_linewidth)

    pyplot.ylim([10, 70])

    ax.set_xlabel("Req rate (req/s)")
    ax.set_ylabel("Mean TPOT (ms)")
    pyplot.tight_layout()
    fig.legend(loc=(0.52, 0.3), frameon=False, markerfirst=False)
    pyplot.savefig(output_path)


def plot_p99_tpot(
    qps_list: list[float],
    offload_tpots: dict[float, list[float]],
    disagg_tpots: list[float],
    output_path: str,
):
    # print(qps_list)
    # print(offload_tpots)
    # print(disagg_tpots)
    # fig, ax = pyplot.subplots(figsize=(3.2, 2)) # 1/3 column
    fig, ax = pyplot.subplots(figsize=PlotConfig.figsize) # 1/4 column

    offload_ratio, offload_tpot = list(offload_tpots.items())[0]

    ax.plot(qps_list, disagg_tpots, marker=PlotConfig.marker_style[0],
            color=PlotConfig.colors[0], markersize=PlotConfig.markersize,
            markerfacecolor='none', markeredgewidth=PlotConfig.markeredgewidth,
            label="vLLM")
    ax.plot(qps_list, offload_tpot, marker=PlotConfig.marker_style[1],
            color=PlotConfig.colors[1], markersize=PlotConfig.markersize,
            markerfacecolor='none', markeredgewidth=PlotConfig.markeredgewidth,
            label=SYS_NAME)

    ax.xaxis.set_major_locator(MultipleLocator(1))
    ax.yaxis.set_major_locator(MultipleLocator(20))

    pyplot.grid(axis='y', color='lightgray', linestyle='--', linewidth=PlotConfig.grid_linewidth)
    pyplot.grid(axis='x', color='lightgray', linestyle='--', linewidth=PlotConfig.grid_linewidth)

    pyplot.ylim([0, 120])

    ax.set_xlabel("Req rate (req/s)")
    ax.set_ylabel("P99 TPOT (ms)")
    pyplot.tight_layout()
    fig.legend(loc=(0.52, 0.3), frameon=False, markerfirst=False)
    pyplot.savefig(output_path)


def plot_stable_thpt(
    qps_list: list[float],
    offload_thpts: dict[float, list[float]],
    disagg_thpts: list[float],
    output_path: str,
):
    # fig, ax = pyplot.subplots(figsize=(3.2, 2)) # 1/3 column
    fig, ax = pyplot.subplots(figsize=PlotConfig.figsize) # 1/4 column

    offload_ratio, offload_thpt = list(offload_thpts.items())[0]

    offload_thpt_per_s = [x * 1000 for x in offload_thpt]
    disagg_thpts_per_s = [x * 1000 for x in disagg_thpts]

    print(f'{offload_thpt_per_s=}')
    print(f'{disagg_thpts_per_s=}')
    speedup = [
        offload_thpt_per_s[i]/disagg_thpts_per_s[i]
        for i in range(len(disagg_thpts_per_s))
    ]
    print(f'{speedup=}, {max(speedup)=}, {min(speedup)=}')

    ax.plot(qps_list, disagg_thpts_per_s, marker=PlotConfig.marker_style[0],
            color=PlotConfig.colors[0], markersize=PlotConfig.markersize,
            markerfacecolor='none', markeredgewidth=PlotConfig.markeredgewidth,
            label="vLLM")
    ax.plot(qps_list, offload_thpt_per_s, marker=PlotConfig.marker_style[1],
            color=PlotConfig.colors[1], markersize=PlotConfig.markersize,
            markerfacecolor='none', markeredgewidth=PlotConfig.markeredgewidth,
            label=SYS_NAME)

    ax.xaxis.set_major_locator(MultipleLocator(1))
    ax.yaxis.set_major_locator(MultipleLocator(400))

    pyplot.grid(axis='y', color='lightgray', linestyle='--', linewidth=PlotConfig.grid_linewidth)
    pyplot.grid(axis='x', color='lightgray', linestyle='--', linewidth=PlotConfig.grid_linewidth)

    pyplot.ylim([400, 2000])

    ax.set_xlabel("Req rate (req/s)")
    ax.set_ylabel("Throughput (tokens/s)")
    pyplot.tight_layout()
    fig.legend(loc=(0.52, 0.3), frameon=False, markerfirst=False)
    pyplot.savefig(output_path)


def plot_fused_thpt_tpot(
    schemes: list[str],
    thpts: dict[str, list[float]],
    tpots: dict[str, list[float]],
    qps_list: list[float],
    output_path: str,
):
    fig, ax1 = pyplot.subplots(figsize=FusedPlotConfig.figsize)
    ax2 = ax1.twinx()

    n_groups = len(thpts[schemes[0]])
    # x_ticks = ['Llama-2 7B', 'Llama-2 13B']
    x_ticks = qps_list
    assert n_groups == len(x_ticks)

    x = numpy.arange(n_groups) # the label locations
    width = FusedPlotConfig.bar_width  # the width of the bars
    offsets = numpy.arange(len(schemes)) * width - width * (len(schemes) - 1) / 2
    offsets = offsets * FusedPlotConfig.bar_padding # add spaces for bars in a group

    for i in range(len(schemes)):
        scheme = schemes[i]
        tag = scheme_to_label[scheme]
        print(scheme, tag)
        target_value = [x * 1000 for x in thpts[scheme]]
        ax1.bar(x + offsets[i], target_value, width, label=tag,
                # hatch=FusedPlotConfig.hatch_style[i],
                color=FusedPlotConfig.colors[i], edgecolor='black', linewidth=1)

    for i in range(len(qps_list)):
        x_values = [i + x for x in offsets]
        y_values = [tpots[scheme][i] for scheme in schemes]
        label = 'Mean TPOT' if i == 0 else None
        ax2.plot(x_values, y_values, marker=FusedPlotConfig.marker_style[0],
                 color='black', markersize=FusedPlotConfig.markersize, label=label,
                 markerfacecolor='white', markeredgewidth=FusedPlotConfig.markeredgewidth)

    pyplot.xticks(x, x_ticks)

    ax1.set_xlabel('Req rate (req/s)')
    ax1.set_ylabel('Throughput (tokens/s)')

    ax2.set_ylabel('Mean TPOT (ms)')
    # ax2.yaxis.label.set_color('red')

    ax1.set_ylim([0, 2000])
    ax2.set_ylim([0, 100])

    ax2.yaxis.set_major_locator(MultipleLocator(20))

    # pyplot.tight_layout()
    # https://www.geeksforgeeks.org/how-to-place-legend-outside-of-the-plot-in-matplotlib/
    # fig.legend(bbox_to_anchor=(1.4, 0.8), frameon=False)

    # handlelength：控制图例中 marker 的宽度，默认值通常是 2
    # handletextpad：控制 marker 和图例文本之间的间距。
    # labelspacing：控制图例项之间的垂直间距。
    # fig.legend(loc=(0.25, 0.69), fontsize=10, frameon=False, handlelength=1)

    ax2.legend(loc=(0.02, 0.81), fontsize=9, frameon=False)

    # fig.legend(ncol=2, loc=(0.17, 0.85), fontsize=10, frameon=False)
    # fig.subplots_adjust(top=0.8)

    ax1.legend(ncol=4, loc=(-0.25, 1.05), fontsize=10, frameon=False, handlelength=1)
    fig.subplots_adjust(top=0.8)


    ax1.grid(axis='y', color='lightgray', linestyle='--')
    ax1.set_axisbelow(True)
    # ax2.set_axisbelow(True)

    pyplot.savefig(output_path, bbox_inches='tight')
    output_path = crop_margins(output_path)
    print(f"Croped to {output_path} successfully.")


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--dataset", type=str, default="sharegpt")
    parser.add_argument("--model", type=str, default="meta-llama/Llama-2-7b-hf")
    parser.add_argument(
        "--qps",
        nargs="+",
        type=str,
        default=[str(qps) for qps in range(1, 5+1)],
    )
    parser.add_argument("--numreqs", type=int, default=250)
    parser.add_argument(
        "--output-dir", type=str, default="evaluation/plots/cross_node_performance"
    )
    parser.add_argument("--disagg-dir", type=str, default="outputs/e2e_perf/disagg/cross_node")
    parser.add_argument("--offload-dir", type=str, default="outputs/e2e_perf/offload/cross_node")
    parser.add_argument("--offload-ratios", nargs="+", type=float, default=[0.6])
    parser.add_argument("--num-execs", type=int, default=1)
    args = parser.parse_args()
    return args

def print_benefits(thpts, tpots, qps_list, index):
    thpt_speedup = [
        thpts[scheme][index]/thpts['baseline'][index]
        for scheme in schemes
    ]
    tpots_res = [
        tpots[scheme][index]/tpots['baseline'][index]
        for scheme in schemes
    ]
    print(f'qps={qps_list[index]}, {thpt_speedup=}')
    print(f'qps={qps_list[index]}, {tpots_res=}')

if __name__ == "__main__":
    args = parse_args()
    print(args)
    configs = [f"{args.dataset}-qps_{qps}-numreqs_{args.numreqs}" for qps in args.qps]
    qps_float_list = [float(qps) for qps in args.qps]
    offload_ratios: list[float] = args.offload_ratios
    offload_ratio = offload_ratios[0]
    offload_dirs = {
        ratio: f"{args.offload_dir}/ratio_{ratio}/{args.model}" for ratio in offload_ratios
    }
    offload_ttfts = {
        ratio: [
            get_ttft_for_config(config, offload_dirs[ratio], args.num_execs)
            for config in configs
        ]
        for ratio in offload_ratios
    }
    disagg_dir = f"{args.disagg_dir}/{args.model}"  
    disagg_ttfts = [
        get_ttft_for_config(config, disagg_dir, args.num_execs)
        for config in configs
    ]
    plot_ttft(qps_float_list, offload_ttfts, disagg_ttfts, f"{args.output_dir}/{args.dataset}_ttft_{offload_ratio}.pdf")

    offload_tpots = {
        ratio: [
            get_tpot_for_config(config, offload_dirs[ratio], args.num_execs)
            for config in configs
        ]
        for ratio in offload_ratios
    }
    disagg_tpots = [
        get_tpot_for_config(config, disagg_dir, args.num_execs)
        for config in configs
    ]
    plot_tpot(qps_float_list, offload_tpots, disagg_tpots, f"{args.output_dir}/{args.dataset}_tpot_{offload_ratio}.pdf")

    offload_p99_tpots = {
        ratio: [
            get_p99_tpot_for_config(config, offload_dirs[ratio], args.num_execs)
            for config in configs
        ]
        for ratio in offload_ratios
    }
    disagg_p99_tpots = [
        get_p99_tpot_for_config(config, disagg_dir, args.num_execs)
        for config in configs
    ]
    plot_p99_tpot(qps_float_list, offload_p99_tpots, disagg_p99_tpots, f"{args.output_dir}/{args.dataset}_p99_tpot_{offload_ratio}.pdf")

    offload_thpts = {
        ratio: [
            get_thpt_for_config(config, offload_dirs[ratio], args.num_execs, result_name="decode.out")
            for config in configs
        ]
        for ratio in offload_ratios
    }
    disagg_thpts = [
        get_thpt_for_config(config, disagg_dir, args.num_execs, result_name="decode.out")
        for config in configs
    ]
    print(offload_thpts)
    print(disagg_thpts)
    plot_stable_thpt(
        qps_float_list, offload_thpts, disagg_thpts, f"{args.output_dir}/{args.dataset}_thpt_{offload_ratio}.pdf"
    )

    schemes = ['baseline', '0.5', '0.6', '0.7']
    thpts = {
        'baseline': disagg_thpts[1:],
        '0.5': offload_thpts[0.5][1:],
        '0.6': offload_thpts[0.6][1:],
        '0.7': offload_thpts[0.7][1:],
    }
    tpots = {
        'baseline': disagg_tpots[1:],
        '0.5': offload_tpots[0.5][1:],
        '0.6': offload_tpots[0.6][1:],
        '0.7': offload_tpots[0.7][1:],
    }
    qps_list = [2, 3, 4, 5]
    plot_fused_thpt_tpot(
        schemes, thpts, tpots, qps_list, f"{args.output_dir}/{args.dataset}_cross_node_fused.pdf"
    )
    print(f'{thpts=}')
    print(f'{tpots=}')
    print_benefits(thpts, tpots, qps_list, 2)
    print_benefits(thpts, tpots, qps_list, 3)
