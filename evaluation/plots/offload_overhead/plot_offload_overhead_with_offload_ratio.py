import argparse
import statistics

from dataclasses import dataclass
from pathlib import Path

import matplotlib.pyplot as plt
from matplotlib.ticker import MultipleLocator

from evaluation.plots.nsys.parser import NsysParser
from evaluation.plots.utils.plot_utils import sample_events

@dataclass
class NvtxEventTrace:
    decode_forward_without_offload: str = "model_runner.py:2605(forward)"
    decode_forward_with_offload: str = "model_runner.py:110(forward)"
    attention_forward: str = "attn_runner.py:371(forward)"


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--offload-ratios",
        nargs="+",
        type=float,
        default=[0, 0.2, 0.4, 0.6, 0.8, 1, 1.2],
    )
    parser.add_argument("--input-len", type=int, default=1024)
    parser.add_argument("--num_locals", type=int, default=32)
    parser.add_argument("--profile-dir", type=str, default="evaluation/results/offload_overhead")
    parser.add_argument(
        "--output-path",
        type=str,
        default="evaluation/plots/offload_overhead/offload_overhead_with_fix_decode_batch.pdf",
    )
    args = parser.parse_args()
    return args


def get_decode_runtime(parser: NsysParser, offload_ratio: float):
    trace = NvtxEventTrace()
    if offload_ratio == 0:
        nvtx_name = trace.decode_forward_without_offload
    else:
        nvtx_name = trace.decode_forward_with_offload
    events = parser.get_nvtx_events(nvtx_name)
    sampled_events = sample_events(events)
    replay_events = [
        parser.get_nvtx_event("replay", e["start"], e["end"], e.global_tid)
        for e in sampled_events
    ]
    cuda_graph_events = [e.cuda_graph_event for e in replay_events]
    cuda_graph_starts = [e["start"] for e in cuda_graph_events]
    cuda_graph_ends = [e["end"] for e in cuda_graph_events]
    cuda_graph_durations = [
        # devide 1e6 to express in mircosecond (ms)
        (end - start) / 1e6 for start, end in zip(cuda_graph_starts, cuda_graph_ends)
    ]
    return statistics.mean(cuda_graph_durations)


def plot_decode_durations_with_fix_decode_batch(
    offload_ratios: list[float], decode_durations: list[float], output_path: str
):
    plt.axhline(decode_durations[0], linestyle="--", label="decode-only")
    plt.plot(offload_ratios[1:], decode_durations[1:], '-^', label="decode+offload")
    plt.xlabel("offload batch : original batch")
    plt.ylabel("decode runtime/ms")
    plt.legend()
    plt.tight_layout()
    plt.savefig(output_path)

def plot_decode_durations_with_fix_decode_batch_new_bar(
    offload_ratios: list[float], decode_durations: list[float], output_path: str
):
    fig, ax = plt.subplots(figsize=(4, 1.8))


    patterns = [None, "////", "\\\\\\\\", "xxxx", "oooo", "////", "\\\\\\\\", "xxxx", None]

    # colors = [str(x) for x in [1, 0.9, 0.9, 0.9, 0.9, 0.45, 0.45, 0.45, 0]]

    # https://matplotlib.org/stable/users/prev_whats_new/dflt_style_changes.html
    # colors = ['C0', 'C1', 'C2', 'C4', 'C5', 'C6', 'C3']
    colors = ['C0', 'C6', 'C2', 'C4', 'C5', 'C6', 'C3']

    import numpy

    n_schemes = 2
    x = numpy.arange(len(offload_ratios) - 1) # the label locations
    width = 0.25  # the width of the bars
    offsets = numpy.arange(n_schemes) * width - width * (n_schemes - 1) / 2
    offsets = offsets * 1.3 # add spaces for bars in a group

    labels = ['decode-only', 'decode+offload']
    ys = [decode_durations[0:1]*(len(offload_ratios) - 1), decode_durations[1:]]
    for i in range(n_schemes):
        ax.bar(x + offsets[i], ys[i], width,
               label=labels[i], hatch=patterns[i], color='white', edgecolor=colors[i], linewidth=1)

    plt.xticks(x, offload_ratios[1:])

    # ax.set_xlabel('Offloaded batch : original batch')
    ax.set_xlabel('Offloading ratio')
    ax.set_ylabel("GPU time (ms)")

    plt.grid(axis='y', color='gray', linestyle='--')
    ax.set_axisbelow(True)

    plt.legend()

    # ax.yaxis.set_major_locator(MultipleLocator(400))

    # plt.xlim([0, 100])
    plt.ylim([0, 40])
    # plt.text(2, 1870, 'A100 HBM BW', color=colors[1])

    # plt.axhline(2048, linestyle="--", color=colors[1])
    # plt.text(8, 1950, 'A100 HBM BW', color=colors[1])

    # plt.show()
    plt.savefig(output_path, bbox_inches='tight')
    print('saved in ' + output_path)


def plot_decode_durations_with_fix_decode_batch_new(
    offload_ratios: list[float], decode_durations: list[float], output_path: str
):
    fig, ax = plt.subplots(figsize=(4, 2.5))

    marker_style = ['^', 'v', 'o', 'D', '<', '>', 's', 'x', '*']
    colors = ['C0', 'C1', 'C2', 'C4', 'C5', 'C6', 'C3']

    # ax.plot(offload_ratios, [decode_durations[0]]*len(offload_ratios),
    #         marker=marker_style[0], color=colors[0], markersize=4,
    #         label='decode-only')
    ax.plot(offload_ratios[1:], decode_durations[1:],
            marker=marker_style[0], color=colors[0], markersize=4,
            label='decode+offload')
    plt.axhline(decode_durations[0], linestyle="--", color=colors[1],
                label="decode-only")
    ax.set_xlabel('Offloaded batch : original batch')
    ax.set_ylabel("GPU time (ms)")

    plt.legend()

    # ax.yaxis.set_major_locator(MultipleLocator(400))

    # plt.xlim([0, 100])
    plt.ylim([0, 40])
    # plt.text(2, 1870, 'A100 HBM BW', color=colors[1])

    # plt.axhline(2048, linestyle="--", color=colors[1])
    # plt.text(8, 1950, 'A100 HBM BW', color=colors[1])

    # plt.show()
    plt.savefig(output_path, bbox_inches='tight')
    print('saved in ' + output_path)


if __name__ == "__main__":
    args = parse_args()
    print(args)
    # profile_names = [
    #     f"offload_overhead_with_decode_fix_offload_ratio_{ratio}_input_len_{args.input_len}_num_locals_{args.num_locals}.sqlite"
    #     for ratio in args.offload_ratios
    # ]
    # profile_paths = [
    #     Path(args.profile_dir, profile_name) for profile_name in profile_names
    # ]
    # nsys_parsers = [NsysParser(profile_path) for profile_path in profile_paths]
    # decode_durations = [
    #     get_decode_runtime(p, ratio) for p, ratio in zip(nsys_parsers, args.offload_ratios)
    # ]
    args.offload_ratios = [0, 0.2, 0.4, 0.6, 0.8, 1, 1.2]
    decode_durations = [21.175435333333333, 23.296963, 23.297447333333334, 23.275441, 24.920024666666666, 27.767392666666666, 32.560455]
    plot_decode_durations_with_fix_decode_batch_new_bar(args.offload_ratios, decode_durations, args.output_path)
