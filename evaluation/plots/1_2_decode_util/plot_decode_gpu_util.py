import argparse
import matplotlib.pyplot as plt
import glob

from dataclasses import dataclass
from vllm.worker.model_runner import CUDAGraphRunner
from evaluation.plots.nsys.parser import NsysParser
from evaluation.plots.nsys.utils import nvtx
from evaluation.plots.utils.plot_utils import (
    get_compute_powers_for_decode_graph,
    get_bandwidths_for_decode_graph,
    NvtxEventName,
    crop_margins,
)

import matplotlib
matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.titlesize'] = 12
matplotlib.rcParams['axes.labelsize'] = 12
matplotlib.rcParams['xtick.labelsize'] = 12
matplotlib.rcParams['ytick.labelsize'] = 12
matplotlib.rcParams['legend.fontsize'] = 12

@dataclass
class PlotConfig:
    figsize = (2.8, 2.2)
    colors = ['#0000ED', '#BF0000']
    marker_style = ['o', 's', '^', 'v', 'D', '<', '>', 's', 'x', '*']
    markersize = 6
    markeredgewidth = 1.5

def get_kernel_stats(profile_path: str, split_tag: str = ""):
    print(f"start processing {profile_path}...")
    p = NsysParser(profile_path)
    trace = NvtxEventName(
        decode_forward=nvtx(CUDAGraphRunner.forward)
    )

    filename = profile_path.rsplit(split_tag, 1)[-1]
    batch_size = int(filename.split('_', 1)[0])

    agg_comp_utils = get_compute_powers_for_decode_graph(p, trace)
    avg_comp_util = agg_comp_utils.float().mean().item()

    agg_bw_utils = get_bandwidths_for_decode_graph(p, trace)
    avg_bw_util = agg_bw_utils.float().mean().item()
    return batch_size, avg_comp_util, avg_bw_util


def get_data(args):
    results = []
    for rpath in glob.glob(f"{args.reports_dir}/*.sqlite"):
        prompt_len, avg_comp_util, avg_bw_util = get_kernel_stats(rpath, split_tag='1_2_decode_util_')
        results.append((prompt_len, avg_comp_util, avg_bw_util))
    sorted_results = sorted(results, key=lambda t: t[0])
    data = {'mem_util': {'x': [], 'y': []}, 'comp_util': {'x': [], 'y': []}}
    data['mem_util']['x'] = [x[0] for x in sorted_results]
    data['mem_util']['y'] = [x[2] for x in sorted_results]
    data['comp_util']['x'] = [x[0] for x in sorted_results]
    data['comp_util']['y'] = [x[1] for x in sorted_results]

    print(data)
    return data

scheme_to_label = {
    'mem_util': 'Mem BW',
    'comp_util': 'Compute',
}

def plot_gpu_util(data, scheme_order, save_path):
    fig, ax = plt.subplots(figsize=PlotConfig.figsize)

    # for i, t in enumerate(data.items()):
    for i, scheme in enumerate(scheme_order):
        # scheme, scatters = t
        scatters = data[scheme]
        ax.plot(scatters['x'], scatters['y'],
            marker=PlotConfig.marker_style[i], color=PlotConfig.colors[i],
            markersize=PlotConfig.markersize, markerfacecolor='none',
            markeredgewidth=PlotConfig.markeredgewidth,
            label=scheme_to_label[scheme])

    # ax.set_xlabel('Inserted items (million)', color='black')
    # ax.set_ylabel('Load factor')
    ax.set_xlabel('Batch size')
    ax.set_ylabel('Resource Util. (%)')

    # # https://jakevdp.github.io/PythonDataScienceHandbook/04.06-customizing-legends.html
    # ax.legend(loc='lower center', ncol=3, fontsize=8)

    # # https://matplotlib.org/3.5.0/gallery/ticks/major_minor_demo.html
    # ax.xaxis.set_major_locator(MultipleLocator(0.2))
    # ax.yaxis.set_major_locator(MultipleLocator(20))

    # https://stackabuse.com/how-to-set-axis-range-xlim-ylim-in-matplotlib/
    # plt.xlim([0, 128])
    # plt.xlim([0, 90])
    plt.ylim([0, 100])

    # https://www.w3schools.com/python/matplotlib_grid.asp
    plt.grid(axis='y', color='lightgray', linestyle='--', linewidth=0.25)
    plt.grid(axis='x', color='lightgray', linestyle='--', linewidth=0.25)

    # # set label for x-axis
    # labels = ['p50', 'p90', 'p99', 'p99.9', 'p99.99', 'Max']
    # plt.xticks(x, labels)

    # # Set the legend
    # # https://stackoverflow.com/questions/4700614/how-to-put-the-legend-outside-the-plot-in-matplotlib
    # # https://matplotlib.org/stable/api/figure_api.html#matplotlib.figure.Figure
    # fig.legend(loc='upper center', ncol=4, fontsize=9, frameon=False)
    # # fig.legend(loc='lower left', bbox_to_anchor=(0.15, 0.8, 0.8, 0.2), ncol=4, fontsize=9, frameon=False)
    # fig.tight_layout()
    # fig.subplots_adjust(top=0.85)
    fig.legend(loc=(0.5, 0.45), fontsize=10, frameon=False)  # 设置背景

    # plt.show()
    plt.savefig(save_path, bbox_inches='tight')
    print('saved in ' + save_path)


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--reports-dir", type=str, default="evaluation/results/1_2_decode_util")
    parser.add_argument("--use-existed-data", action="store_true")
    parser.add_argument("--no-crop-margin", action="store_true")
    parser.add_argument(
        "--output-path",
        type=str,
        default="evaluation/plots/1_2_decode_util/decode_resource_util.pdf",
    )
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    if args.use_existed_data:
        data = {'mem_util': {'x': [8, 16, 24, 32, 40, 48, 56, 64, 72, 80], 'y': [44.71439743041992, 47.69132614135742, 46.88181686401367, 52.883968353271484, 47.97259521484375, 53.53158950805664, 53.829833984375, 58.20305252075195, 55.8266716003418, 62.9099006652832]}, 'comp_util': {'x': [8, 16, 24, 32, 40, 48, 56, 64, 72, 80], 'y': [18.12897491455078, 15.94257640838623, 13.351555824279785, 13.100055694580078, 15.522521018981934, 15.543441772460938, 14.199435234069824, 14.042107582092285, 16.404930114746094, 15.151731491088867]}}
    else:
        data = get_data(args)
    scheme_order = ['comp_util', 'mem_util']
    plot_gpu_util(data, scheme_order, args.output_path)
    if not args.no_crop_margin:
        crop_path = crop_margins(args.output_path)
        print(f"Croped {args.output_path} to {crop_path} successfully.")
