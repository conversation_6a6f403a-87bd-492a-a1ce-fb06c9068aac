import argparse
import glob
import itertools
import matplotlib
import numpy
import statistics

from dataclasses import dataclass
from matplotlib import pyplot
from evaluation.plots.nsys.parser import NsysParser
from evaluation.plots.nsys.utils import A100_80GB_META_DATA
from evaluation.plots.utils.model_utils import get_model_compute_flops, to_model_compute_flops
from evaluation.plots.utils.plot_utils import (
    NvtxEventName,
    get_bandwidths_for_prefill,
    get_compute_powers_for_decode_graph,
    CommonColors,
    crop_margins,
)

matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 10
matplotlib.rcParams['axes.titlesize'] = 10
matplotlib.rcParams['axes.labelsize'] = 10
matplotlib.rcParams['xtick.labelsize'] = 10
matplotlib.rcParams['ytick.labelsize'] = 10
matplotlib.rcParams['legend.fontsize'] = 10

scheme_to_label = {
    'vllm': 'vLLM',
    '0.2': 'Offload 20%',
    '0.4': 'Offload 40%',
    '0.6': 'Offload 60%',
    '0.8': 'Offload 80%',
}

@dataclass
class PlotConfig:
    figsize = (3, 2)
    # colors = ['#0000ED', '#EAAABB', '#D45555', '#BF0000']
    colors = [CommonColors.gradient_blues[2]] + CommonColors.gradient_reds[1:]
    marker_style = ['o', 's', '^', 'v', 'D', '<', '>', 's', 'x', '*']
    n_groups = 2
    bar_width = 0.10
    bar_padding = 1.4


def get_prefill_bandwidth_for_disagg(
    profile_dir: str, model_name: str, num_locals: int, trace: NvtxEventName
):
    profile_paths = glob.glob(f"{profile_dir}/disagg_prefill_{model_name}_num_locals_{num_locals}*.sqlite")
    assert (
        len(profile_paths) > 0
    ), f"{profile_dir}/disagg_prefill_{model_name}.sqlite not exist"
    profile_path = profile_paths[0]
    parser = NsysParser(profile_path)
    bandwidths = get_bandwidths_for_prefill(parser, trace)
    avg_bandwidth = statistics.fmean(bandwidths)
    return avg_bandwidth


def get_prefill_bandwidth_for_offload(
    profile_dir: str, offload_ratio: float, model_name: str, num_locals: int, trace: NvtxEventName
):
    profile_paths = glob.glob(
        f"{profile_dir}/offload_prefill_{offload_ratio}_{model_name}_num_locals_{num_locals}*.sqlite"
    )
    assert (
        len(profile_paths) > 0
    ), f"{profile_dir}/offload_prefill_{offload_ratio}_{model_name}_num_locals_{num_locals}*.sqlite not exist"
    profile_path = profile_paths[0]
    parser = NsysParser(profile_path)
    bandwidths = get_bandwidths_for_prefill(parser, trace)
    avg_bandwidth = statistics.fmean(bandwidths)
    return avg_bandwidth


def get_decode_compute_power_for_disagg_by_time(
    profile_dir: str,
    model_name: str,
    batch: int,
    trace: NvtxEventName,
):
    profile_path = glob.glob(f"{profile_dir}/disagg_decode*.sqlite")[0]
    parser = NsysParser(profile_path)
    forward_events = parser.get_nvtx_events(trace.decode_forward)
    cuda_graph_events = [e.cuda_graph_event for e in forward_events]
    model = to_model_compute_flops(model_name)
    decode_flops = get_model_compute_flops(model, batch)
    decode_durations = [e["end"] - e["start"] for e in cuda_graph_events]
    # devide 1e3 convert to TFLOPS/s (FLOPS / ns -> TFLOPS/s)
    compute_powers = [
        decode_flops / duration / 1e3 / A100_80GB_META_DATA.PEAK_FP16_TFLOPS * 100
        for duration in decode_durations
    ]
    avg_compute_power = statistics.fmean(compute_powers)
    return avg_compute_power


def get_decode_compute_power_for_disagg(
    profile_dir: str, model_name: str, batch: int, trace: NvtxEventName
):
    profile_path = glob.glob(f"{profile_dir}/disagg_decode_{model_name}_num_locals_{batch}*.sqlite")[0]
    parser = NsysParser(profile_path)
    compute_powers = get_compute_powers_for_decode_graph(parser, trace)
    avg_compute_power = statistics.fmean(compute_powers)
    return avg_compute_power


def get_decode_compute_power_for_offload_by_time(
    profile_dir: str,
    offload_ratio: float,
    model_name: str,
    num_locals: int,
    trace: NvtxEventName,
):
    batch = int(num_locals * (1 + offload_ratio))
    profile_path = glob.glob(f"{profile_dir}/offload_decode_{offload_ratio}_{model_name}*.sqlite")[0]
    parser = NsysParser(profile_path)
    forward_events = parser.get_nvtx_events(trace.decode_forward)
    cuda_graph_events = [e.cuda_graph_event for e in forward_events]
    model = to_model_compute_flops(model_name)
    decode_flops = get_model_compute_flops(model, batch)
    decode_durations = [e["end"] - e["start"] for e in cuda_graph_events]
    # devide 1e3 convert to TFLOPS/s (FLOPS / ns -> TFLOPS/s)
    compute_powers = [
        decode_flops / duration / 1e3 / A100_80GB_META_DATA.PEAK_FP16_TFLOPS * 100
        for duration in decode_durations
    ]
    avg_compute_power = statistics.fmean(compute_powers)
    return avg_compute_power


def get_decode_compute_power_for_offload(
    profile_dir: str,
    offload_ratio: float,
    model_name: str,
    num_locals: int,
    trace: NvtxEventName,
):
    profile_path = glob.glob(f"{profile_dir}/offload_decode_{offload_ratio}_{model_name}_num_locals_{num_locals}*.sqlite")[0]
    parser = NsysParser(profile_path)
    compute_powers = get_compute_powers_for_decode_graph(parser, trace)
    avg_compute_power = statistics.fmean(compute_powers)
    return avg_compute_power


def plot_prefill_bandwidth(
    prefill_bandwidths,
    offload_ratios: list[float],
    output_path: str,
):
    schemes = ['vllm'] + [str(r) for r in offload_ratios]

    fig, ax1 = pyplot.subplots(figsize=PlotConfig.figsize)
    # ax2 = ax1.twinx()

    x_ticks = ['Llama-2 7B', 'Llama-2 13B']
    assert PlotConfig.n_groups == len(x_ticks)

    x = numpy.arange(PlotConfig.n_groups) # the label locations
    width = PlotConfig.bar_width  # the width of the bars
    offsets = numpy.arange(len(schemes)) * width - width * (len(schemes) - 1) / 2
    offsets = offsets * PlotConfig.bar_padding # add spaces for bars in a group

    # patterns = [None, "////", "\\\\\\\\", "xxxx", "oooo", "////", "\\\\\\\\", "xxxx", None]

    # colors = ['C0', 'C6', 'C2', 'C4', 'C5', 'C6', 'C3']

    group_labels = [scheme_to_label[s] for s in schemes]

    for i in range(len(schemes)):
        scheme = schemes[i]
        tag = scheme_to_label[scheme]
        print(scheme, tag)
        target_value = prefill_bandwidths[scheme]
        # ax1.bar(x + offsets[i], target_value, width, label=tag, hatch=patterns[i],
        #         color='white', edgecolor=colors[i], linewidth=1)
        # ax1.bar(x + offsets[i], target_value, width, label=tag,
        #         color=PlotConfig.colors[i])
        ax1.bar(x + offsets[i], target_value, width, label=tag,
                color=PlotConfig.colors[i], edgecolor='black', linewidth=1)

    pyplot.xticks(x, x_ticks)

    # ax1.set_xlabel('Prompt length')
    # ax1.set_ylabel('Mem BW Util. (%)')
    ax1.set_ylabel('Mem BW (GB/s)')

    # ax2.set_ylabel('Execute Time Ratio (%)', color='red')
    # ax2.yaxis.label.set_color('red')

    ax1.set_ylim([0, 1000])
    # ax2.set_ylim([0, 100])

    # ax1.legend(group_labels, ncol=4, loc='upper center', fontsize=9)
    # ax1.legend(group_labels, frameon=False)
    pyplot.grid(axis='y', color='lightgray', linestyle='--')
    ax1.set_axisbelow(True)
    # ax2.set_axisbelow(True)

    pyplot.savefig(output_path, bbox_inches='tight')


def plot_decode_compute_power(
    decode_compute_powers,
    offload_ratios: list[float],
    output_path: str,
):
    schemes = ['vllm'] + [str(r) for r in offload_ratios]

    fig, ax1 = pyplot.subplots(figsize=PlotConfig.figsize)
    # ax2 = ax1.twinx()

    x_ticks = ['Llama-2 7B', 'Llama-2 13B']
    assert PlotConfig.n_groups == len(x_ticks)

    x = numpy.arange(PlotConfig.n_groups) # the label locations
    width = PlotConfig.bar_width  # the width of the bars
    offsets = numpy.arange(len(schemes)) * width - width * (len(schemes) - 1) / 2
    offsets = offsets * PlotConfig.bar_padding # add spaces for bars in a group

    group_labels = [scheme_to_label[s] for s in schemes]

    for i in range(len(schemes)):
        scheme = schemes[i]
        tag = scheme_to_label[scheme]
        print(scheme, tag)
        target_value = decode_compute_powers[scheme]
        # ax1.bar(x + offsets[i], target_value, width, label=tag,
        #         color=PlotConfig.colors[i])
        ax1.bar(x + offsets[i], target_value, width, label=tag,
                color=PlotConfig.colors[i], edgecolor='black', linewidth=1)

    pyplot.xticks(x, x_ticks)

    # ax1.set_xlabel('Prompt length')
    # ax1.set_ylabel('Comp. Util. (%)')
    ax1.set_ylabel('Compute Power (TFLOPS)')

    # ax2.set_ylabel('Execute Time Ratio (%)', color='red')
    # ax2.yaxis.label.set_color('red')

    ax1.set_ylim([0, 150])
    # ax2.set_ylim([0, 100])

    # ax1.legend(group_labels, ncol=4, loc='upper center', fontsize=9)
    # ax1.legend(group_labels, frameon=False)
    pyplot.grid(axis='y', color='lightgray', linestyle='--')
    ax1.set_axisbelow(True)
    # ax2.set_axisbelow(True)

    pyplot.savefig(output_path, bbox_inches='tight')


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--profile-dir", type=str, default="evaluation/results/resource_utilization"
    )
    parser.add_argument("--offload-ratios", nargs="+", type=float, default=[0.4, 0.6, 0.8])
    parser.add_argument("--models_name", nargs="+", type=str, default=["Llama-2-7b-hf", "Llama-2-13b-hf"])
    parser.add_argument("--num-locals", nargs="+", type=int, default=[55, 50])
    parser.add_argument("--output-dir", type=str, default="evaluation/plots/resource_utilization")
    parser.add_argument("--crop-margin", action="store_true")
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    print(args)
    offload_ratios: list[float] = args.offload_ratios
    models_name: list[str] = args.models_name
    num_locals_list: list[int] = args.num_locals
    disagg_trace = NvtxEventName(
        prefill_forward="llama.py:550(forward)",
        decode_forward="model_runner.py:2687(forward)",
    )
    offload_trace = NvtxEventName(
        prefill_forward="llama.py:245(forward)",
        decode_forward="model_runner.py:110(forward)",
    )

    disagg_prefill_bandwidth = [
        get_prefill_bandwidth_for_disagg(args.profile_dir, model_name, num_locals, disagg_trace) / 100 * A100_80GB_META_DATA.MEMORY_BANDWIDTH
        for (model_name, num_locals) in zip(models_name, num_locals_list)
    ]
    offload_prefill_bandwidths = {
        (ratio, model): get_prefill_bandwidth_for_offload(
            args.profile_dir, ratio, model, num_locals, offload_trace
        )
        for ratio, (model, num_locals) in itertools.product(args.offload_ratios, zip(models_name, num_locals_list))
    }
    print(disagg_prefill_bandwidth, offload_prefill_bandwidths)
    prefill_bandwidths = {"vllm": disagg_prefill_bandwidth}
    for i, ratio in enumerate(offload_ratios):
        prefill_bandwidths[str(ratio)] = [
            offload_prefill_bandwidths[(ratio, model_name)] / 100 * A100_80GB_META_DATA.MEMORY_BANDWIDTH
            for model_name in models_name
        ]
    print(f'{prefill_bandwidths=}')
    plot_prefill_bandwidth(
        prefill_bandwidths, offload_ratios, f"{args.output_dir}/prefill_bandwidth.pdf"
    )

    disagg_decode_compute_power = [
        get_decode_compute_power_for_disagg(
            args.profile_dir, model_name, num_locals, disagg_trace
        )
        for model_name, num_locals in zip(models_name, num_locals_list)
    ]
    offload_decode_compute_powers = {
        (ratio, model): get_decode_compute_power_for_offload(
            args.profile_dir, ratio, model, num_locals, offload_trace
        )
        for ratio, (model, num_locals) in itertools.product(
            offload_ratios, zip(models_name, num_locals_list)
        )
    }
    print(disagg_decode_compute_power, offload_decode_compute_powers)
    decode_compute_powers = {"vllm": disagg_decode_compute_power}
    for i, ratio in enumerate(offload_ratios):
        decode_compute_powers[str(ratio)] = [
            offload_decode_compute_powers[(ratio, model_name)] / 100 * A100_80GB_META_DATA.PEAK_FP16_TFLOPS
            for model_name in models_name
        ]
    print(f'{decode_compute_powers=}')
    plot_decode_compute_power(
        decode_compute_powers, offload_ratios, f"{args.output_dir}/decode_compute.pdf"
    )
    if args.crop_margin:
        crop_path = crop_margins(f"{args.output_dir}/prefill_bandwidth.pdf")
        print(f"Cropped to {crop_path} successfully.")
        crop_path = crop_margins(f"{args.output_dir}/decode_compute.pdf")
        print(f"Cropped to {crop_path} successfully.")
