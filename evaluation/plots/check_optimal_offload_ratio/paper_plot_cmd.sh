#!/bin/bash
# sharegpt (debug use)
python -m evaluation.plots.check_optimal_offload_ratio.plot_sharegpt_diff_ratio --dataset sharegpt --offload-ratios 0.4 0.6 0.7 0.8 --qps 1 1.5 2 2.5 3 3.2 3.4 3.6 4 4.4 4.6 4.8
# python -m evaluation.plots.check_optimal_offload_ratio.plot_sharegpt_diff_ratio --disagg-dir outputs/e2e_perf/disagg/meta-llama/Llama-2-13b-hf --dataset open-thoughts --offload-ratios 0.6 0.7 0.8 --numreqs 30 --qps 1 1.5 2 2.5 3 3.5
# paper
python -m evaluation.plots.check_optimal_offload_ratio.plot_sharegpt_diff_ratio --dataset sharegpt --offload-ratios 0.4 0.6 0.7 0.8 --qps 3 3.2 3.4 3.6 4 4.4 4.6 4.8
