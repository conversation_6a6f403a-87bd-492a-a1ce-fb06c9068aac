import argparse

from matplotlib import pyplot
from evaluation.plots.dataset_performance.utils import (
    get_ttft_for_config,
    get_tpot_for_config,
    get_thpt_for_config,
    plot_ttft,
    plot_tpot,
    plot_stable_thpt,
)


def plot_e2e_performance(
    qps_list: list[float],
    offload_ttfts: dict[float, list[float]],
    disagg_ttfts: list[float],
    offload_tpots: dict[float, list[float]],
    disagg_tpots: list[float],
    offload_thpts: dict[float, list[float]],
    disagg_thpts: list[float],
    output_path: str,
):
    figsize = 3
    fig, _ = pyplot.subplots(1, 3, figsize=(3 * figsize, figsize))

    for offload_ratio, offload_ttft in offload_ttfts.items():
        fig.axes[0].plot(
            qps_list, offload_ttft, "-o", label=f"offload (ratio={offload_ratio})"
        )
    fig.axes[0].plot(qps_list, disagg_ttfts, "--o", label="disagg")
    fig.axes[0].set_xlabel("QPS")
    fig.axes[0].set_ylabel("TTFT (ms)")
    fig.axes[0].set_ylim(0, 5000)

    for offload_ratio, offload_tpot in offload_tpots.items():
        fig.axes[1].plot(
            qps_list, offload_tpot, "-o", label=f"offload (ratio={offload_ratio})"
        )
    fig.axes[1].plot(qps_list, disagg_tpots, "--o", label="disagg")
    fig.axes[1].set_xlabel("QPS")
    fig.axes[1].set_ylabel("TPOT (ms)")

    for offload_ratio, offload_thpt in offload_thpts.items():
        fig.axes[2].plot(
            qps_list, offload_thpt, "-o", label=f"offload (ratio={offload_ratio})"
        )
    fig.axes[2].plot(qps_list, disagg_thpts, "--o", label="disagg")
    fig.axes[2].set_xlabel("QPS")
    fig.axes[2].set_ylabel("Throughput (tokens/ms)")

    handles, labels = fig.axes[0].get_legend_handles_labels()
    fig.legend(handles, labels)
    pyplot.tight_layout()
    pyplot.savefig(output_path)
    pyplot.clf()


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--dataset", type=str, default="sharegpt")
    parser.add_argument(
        "--qps",
        nargs="+",
        type=float,
        default=[1, 1.5, 2, 2.5, 3, 3.2, 3.4, 3.6, 4, 4.4, 4.6, 4.8],
    )
    parser.add_argument("--numreqs", type=int, default=250)
    parser.add_argument(
        "--output-dir", type=str, default="evaluation/plots/dataset_performance"
    )
    parser.add_argument("--disagg-dir", type=str, default="outputs/e2e_perf/disagg")
    parser.add_argument("--offload-dir", type=str, default="outputs/e2e_perf/offload")
    parser.add_argument("--offload-ratios", nargs="+", type=float, default=[0.6, 0.4, 0.8])
    parser.add_argument("--num-execs", type=int, default=3)
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    configs = [f"{args.dataset}-qps_{qps}-numreqs_{args.numreqs}" for qps in args.qps]
    offload_ratios: list[float] = args.offload_ratios
    offload_dirs = {
        ratio: f"{args.offload_dir}/ratio_{ratio}" for ratio in offload_ratios
    }
    offload_ttfts = {
        ratio: [
            get_ttft_for_config(config, offload_dirs[ratio], args.num_execs)
            for config in configs
        ]
        for ratio in offload_ratios
    }
    disagg_ttfts = [
        get_ttft_for_config(config, args.disagg_dir, args.num_execs)
        for config in configs
    ]
    plot_ttft(args.qps, offload_ttfts, disagg_ttfts, f"{args.output_dir}/{args.dataset}_ttft.pdf")

    offload_tpots = {
        ratio: [
            get_tpot_for_config(config, offload_dirs[ratio], args.num_execs)
            for config in configs
        ]
        for ratio in offload_ratios
    }
    disagg_tpots = [
        get_tpot_for_config(config, args.disagg_dir, args.num_execs)
        for config in configs
    ]
    plot_tpot(args.qps, offload_tpots, disagg_tpots, f"{args.output_dir}/{args.dataset}_tpot.pdf")

    offload_thpts = {
        ratio: [
            get_thpt_for_config(config, offload_dirs[ratio], args.num_execs)
            for config in configs
        ]
        for ratio in offload_ratios
    }
    disagg_thpts = [
        get_thpt_for_config(config, args.disagg_dir, args.num_execs)
        for config in configs
    ]
    plot_stable_thpt(
        args.qps, offload_thpts, disagg_thpts, f"{args.output_dir}/{args.dataset}_thpt.pdf"
    )

    plot_e2e_performance(
        args.qps,
        offload_ttfts,
        disagg_ttfts,
        offload_tpots,
        disagg_tpots,
        offload_thpts,
        disagg_thpts,
        f"{args.output_dir}/{args.dataset}_performance.pdf",
    )
