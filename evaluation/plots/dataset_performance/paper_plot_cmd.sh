#!/bin/bash
# sharegpt
python -m evaluation.plots.dataset_performance.plot_sharegpt_llama_2_7b_for_paper --offload-ratios 0.7 --qps 1 1.5 2 2.5 3 3.2 3.4 3.6 4 4.4 4.6 4.8
python -m evaluation.plots.dataset_performance.plot_sharegpt_llama_2_13b_for_paper --offload-ratios 0.7 --qps 1 1.5 2 2.5 3 3.5
# open-thoughts
python -m evaluation.plots.dataset_performance.plot_open_thoughts_llama_2_7b_for_paper --offload-ratios 0.8 --qps 2 2.5 3 3.5 4 4.5 5
python -m evaluation.plots.dataset_performance.plot_open_thoughts_llama_2_13b_for_paper --offload-ratios 0.8 --qps 1 1.5 2 2.5 3 3.5
