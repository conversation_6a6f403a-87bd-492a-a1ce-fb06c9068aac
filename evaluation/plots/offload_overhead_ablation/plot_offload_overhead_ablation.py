import argparse
import itertools
import statistics
import matplotlib
import numpy

from matplotlib import pyplot
from dataclasses import dataclass

from evaluation.utils.output_parser import parse_gpu_time

from evaluation.plots.utils.plot_utils import (
    CommonColors,
)

matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 10
matplotlib.rcParams['axes.titlesize'] = 10
matplotlib.rcParams['axes.labelsize'] = 10
matplotlib.rcParams['xtick.labelsize'] = 10
matplotlib.rcParams['ytick.labelsize'] = 10
matplotlib.rcParams['legend.fontsize'] = 10

scheme_to_label = {
    'native': 'Baseline',
    'D1': '+Grouping qkv',
    'D1+D2': '+Ordered Assembling',
    'D1+D2+D3': '+2D CUDA graph'
}

# https://www.codeeeee.com/color/picker.html
@dataclass
class PlotConfig:
    figsize = (3.3, 1.6)
    # colors = ['#0000ED', '#EAAABB', '#D45555', '#BF0000']
    # colors = ['white', '#72ACD1', '#408EC1', '#07428E', '#BF0000']
    # colors = ['white', '#85D6E7', '#408EC1', '#07428E', '#BF0000']
    colors = ['white'] + CommonColors.gradient_reds[1:]
    marker_style = ['o', 's', '^', 'v', 'D', '<', '>', 's', 'x', '*']
    n_groups = 2
    bar_width = 0.14
    bar_padding = 1.4

def get_avg_step_time(path: str, num_warmups: int):
    with open(path) as f:
        lines = f.readlines()
    exec_time_lines = [line for line in lines if "execute model with" in line]
    decode_lines = [line for line in exec_time_lines if "model_runner.py" in line]
    attn_lines = [line for line in exec_time_lines if "attn_runner.py" in line]
    decode_times = [parse_gpu_time(line) for line in decode_lines]
    attn_times = [parse_gpu_time(line) for line in attn_lines]
    step_times = [
        min(decode_time, attn_time)
        for decode_time, attn_time in zip(decode_times, attn_times)
    ]
    avg_step_time = statistics.fmean(step_times[num_warmups:])
    return avg_step_time

def process_data(
    step_times: dict[tuple[int, int], float], num_locals: list[int], abla_tecs: list[int], tec_names: list[str], output_path: str
):
    schemes = tec_names
    data = {}

    for i, scheme in enumerate(schemes):
        stats = [
            step_times[(bs, i)] for bs in num_locals
        ]
        data[scheme] = stats
    return schemes, data


def plot_offload_overhead_ablation(
    schemes: list[str],
    data: dict[str, list[float]],
    num_local_list: list[int],
    output_path: str,
):
    fig, ax1 = pyplot.subplots(figsize=PlotConfig.figsize)
    # ax2 = ax1.twinx()

    n_groups = len(data[schemes[0]])
    # x_ticks = ['Llama-2 7B', 'Llama-2 13B']
    x_ticks = num_locals_list
    assert n_groups == len(x_ticks)

    x = numpy.arange(n_groups) # the label locations
    width = PlotConfig.bar_width  # the width of the bars
    offsets = numpy.arange(len(schemes)) * width - width * (len(schemes) - 1) / 2
    offsets = offsets * PlotConfig.bar_padding # add spaces for bars in a group

    # patterns = [None, "////", "\\\\\\\\", "xxxx", "oooo", "////", "\\\\\\\\", "xxxx", None]

    # colors = ['C0', 'C6', 'C2', 'C4', 'C5', 'C6', 'C3']

    group_labels = [scheme_to_label[s] for s in schemes]

    for i in range(len(schemes)):
        scheme = schemes[i]
        tag = scheme_to_label[scheme]
        print(scheme, tag)
        target_value = data[scheme]
        # ax1.bar(x + offsets[i], target_value, width, label=tag, hatch=patterns[i],
        #         color='white', edgecolor=colors[i], linewidth=1)
        ax1.bar(x + offsets[i], target_value, width, label=tag, 
                color=PlotConfig.colors[i], edgecolor='black', linewidth=1)

    pyplot.xticks(x, x_ticks)

    ax1.set_xlabel('Batch size')
    ax1.set_ylabel('TPOT (ms)')

    # ax2.set_ylabel('Execute Time Ratio (%)', color='red')
    # ax2.yaxis.label.set_color('red')

    ax1.set_ylim([0, 80])
    # ax2.set_ylim([0, 100])

    # https://www.geeksforgeeks.org/how-to-place-legend-outside-of-the-plot-in-matplotlib/
    fig.legend(bbox_to_anchor=(1.5, 0.8), frameon=False)

    pyplot.grid(axis='y', color='lightgray', linestyle='--')
    ax1.set_axisbelow(True)
    # ax2.set_axisbelow(True)

    pyplot.savefig(output_path, bbox_inches='tight')


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--log-dir", type=str, default="evaluation/results/offload_overhead_ablation"
    )
    parser.add_argument("--num-locals", nargs="+", type=int, default=[10, 30, 50])
    parser.add_argument("--abla-tecs", nargs="+", type=int, default=[0, 1, 2, 3])
    parser.add_argument("--num-warmups", type=int, default=2)
    parser.add_argument(
        "--output-path",
        type=str,
        default="evaluation/plots/offload_overhead_ablation/overlap_ablation.pdf",
    )
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    tec_names = ["native", "D1", "D1+D2", "D1+D2+D3"]
    num_locals_list: list[int] = args.num_locals
    abla_tecs: list[int] = args.abla_tecs
    configs = {
        (num_local, abla_tec): f"num_locals_{num_local}-abla_tec_{abla_tec}"
        for num_local, abla_tec in itertools.product(num_locals_list, abla_tecs)
    }
    output_paths = {
        (num_local, abla_tec): f"{args.log_dir}/{config}.out"
        for (num_local, abla_tec), config in configs.items()
    }
    step_times = {
        (num_local, abla_tec): get_avg_step_time(path, args.num_warmups)
        for (num_local, abla_tec), path in output_paths.items()
    }
    print(f'{step_times=}')
    print(f'{num_locals_list=}')
    print(f'{abla_tecs=}')
    print(f'{tec_names=}')

    schemes, data = process_data(
        step_times, num_locals_list, abla_tecs, tec_names, args.output_path
    )
    print(f'{schemes=}')
    print(f'{data=}')
    plot_offload_overhead_ablation(
        schemes, data, num_locals_list, args.output_path
    )
