import argparse
import matplotlib.pyplot as plt
import numpy
import glob
import torch
import logging

from typing import Any, Optional, List
from evaluation.plots.nsys import utils, sqlite_parser
from evaluation.plots.nsys.parser import NsysParser, Event
from evaluation.plots.nsys.utils import MetricNames
from evaluation.plots.utils.plot_utils import crop_margins, CommonColors
from evaluation.plots.utils.model_utils import (
    LLAMA2_7B,
    ModelOperator,
    get_gemm_compute_flops,
    get_attn_compute_flops,
)
from evaluation.plots.nsys.utils import A100_80GB_META_DATA

from dataclasses import dataclass
from matplotlib.ticker import MultipleLocator

@dataclass
class PlotConfig:
    figsize = (3.2, 2)
    colors = CommonColors.gradient_blues
    marker_style = ['o', 's', '^', 'v', 'D', '<', '>', 's', 'x', '*']
    markersize = 5
    markeredgewidth = 0.5
    bar_width = 0.13  # the width of the bars
    bar_padding = 1.4 # add spaces for bars in a group

import matplotlib
matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.titlesize'] = 12
matplotlib.rcParams['axes.labelsize'] = 12
matplotlib.rcParams['xtick.labelsize'] = 10
matplotlib.rcParams['ytick.labelsize'] = 10
matplotlib.rcParams['legend.fontsize'] = 12

scheme_to_label = {
    'qkv': 'QKV_proj',
    'attn': 'Attn',
    'o': 'O_proj',
    'ffn': 'FFN',
}

@dataclass
class PrefillKernelPoints:
    layer_line_num: str = "llama.py:244(forward)"
    qkv_line_num: str = "linear.py:366(forward)"
    attn_line_num: str = "prefill.py:1471(forward)"
    o_line_num: str = "linear.py:1066(forward)"
    ffn1_line_num: str = "linear.py:366(forward)"
    ffn2_line_num: str = "linear.py:1066(forward)"


class ReportKernelPoints:

    def __init__(self, layer: str, qkv: str, attn: str, o: str,
                 ffn1: str, ffn2: str):
        self.layer_line_num = layer
        self.qkv_line_num = qkv
        self.attn_line_num = attn
        self.o_line_num = o
        self.ffn1_line_num = ffn1
        self.ffn2_line_num = ffn2

def get_global_id(p: NsysParser, kernel_points: ReportKernelPoints):
    attn_es = p.get_nvtx_events(kernel_points.attn_line_num)
    first_attn_e = attn_es[0]
    thread_id = first_attn_e['globalTid'] % 0x1000000
    global_pid = (first_attn_e['globalTid'] // 0x1000000) * 0x1000000
    print('>>> thread_id =', thread_id, 'from', first_attn_e['globalTid'])
    return thread_id, first_attn_e['globalTid'], global_pid

def get_kernel_stats(profile_path: str,
                     kernel_points: ReportKernelPoints,
                     roi_start: Optional[int] = None,
                     roi_end: Optional[int] = None,
                     split_tag: str = ''):
    print(f'start processing {profile_path}')
    p = NsysParser(profile_path)

    filename = profile_path.rsplit(split_tag, 1)[-1]
    prompt_len = int(filename.split('_', 1)[0])

    # get thread id
    _, global_tid, global_pid = get_global_id(p, kernel_points)

    # set thread ID for parser
    p.set_global_tid_for_query(global_tid)

    es = p.get_nvtx_events(kernel_points.layer_line_num, roi_start, roi_end)
    # print(pprint.pformat(es[:4]))
    qkv_latencies, attn_latencies = [], []
    o_latencies = []
    ffn1_latencies = []
    ffn2_latencies = []
    ffn_latencies = []
    layer_latencies = []

    qkv_bws = ()
    attn_bws = ()
    o_bws = ()
    ffn1_bws = ()
    ffn2_bws = ()
    ffn_bws = ()

    qkv_comps = ()
    attn_comps = ()
    o_comps = ()
    ffn1_comps = ()
    ffn2_comps = ()
    ffn_comps = ()
    for idx, layer_e in enumerate(es[:4]):
        # set global IDs for events
        layer_e.set_global_tid_for_query(global_tid)
        layer_e.set_global_pid_for_query(global_pid)

        # skip es[0] to avoid cold start effect
        if idx == 0:
            continue
        # print('start_time from', es[idx - 1])
        start_time = es[idx - 1]['end']
        # print('>>>>>> curr event', pprint.pformat(es[idx]))
        # print('end_time from', es[idx + 1])
        end_time = es[idx + 1]['start'] if idx < len(es) - 1 else None
        print(f'[start_time, end_time] {start_time, end_time}')
        linear_es = p.get_nvtx_events("linear", start_time, end_time)
        # print('linear_es', pprint.pformat(linear_es))
        assert len(linear_es) == 4, {len(linear_es)}

        # set thread ID for events
        for linear_e in linear_es:
            linear_e.set_global_tid_for_query(global_tid)
            linear_e.set_global_pid_for_query(global_pid)

        latencies = [
            (li.cuda_end_time - li.cuda_start_time) for li in linear_es
        ]

        # print('latencies', pprint.pformat(latencies))
        qkv_latencies.append(latencies[0])
        o_latencies.append(latencies[1])
        ffn1_latencies.append(latencies[2])
        ffn2_latencies.append(latencies[3])

        qkv_bws += (p.get_events_dram_bw(linear_es[:1])[0],)
        o_bws += (p.get_events_dram_bw(linear_es[1:2])[0],)
        ffn1_bws += (p.get_events_dram_bw(linear_es[2:3])[0],)
        ffn2_bws += (p.get_events_dram_bw(linear_es[3:])[0],)

        qkv_comps += (p.get_events_tc_active(linear_es[:1])[0],)
        o_comps += (p.get_events_tc_active(linear_es[1:2])[0],)
        ffn1_comps += (p.get_events_tc_active(linear_es[2:3])[0],)
        ffn2_comps += (p.get_events_tc_active(linear_es[3:])[0],)

        # extract attn stats
        attn_es = p.get_nvtx_events(kernel_points.attn_line_num, start_time, end_time)
        assert len(attn_es) == 1, {len(attn_es)}
        attn_e = attn_es[0]

        # set thread ID for events
        attn_e.set_global_tid_for_query(global_tid)
        attn_e.set_global_pid_for_query(global_pid)

        attn_latency = attn_e.cuda_end_time - attn_e.cuda_start_time
        attn_latencies.append(attn_latency)
        attn_bws += (p.get_events_dram_bw(attn_es)[0],)
        attn_comps += (p.get_events_tc_active(attn_es)[0],)

        # from pprint import pformat
        # print(f"idx: {idx}, {pformat(e)}")
        layer_latencies.append(layer_e.cuda_end_time - layer_e.cuda_start_time)
    
    ffn_latencies = [x1 + x2 for x1, x2 in zip(ffn1_latencies, ffn2_latencies)]
    ffn_bws = [torch.cat((x1, x2)) for x1, x2 in zip(ffn1_bws, ffn2_bws)]
    ffn_comps = [torch.cat((x1, x2)) for x1, x2 in zip(ffn1_comps, ffn2_comps)]

    agg_latencies = {
        # devide 1e6 to express in mircosecond (ms)
        "layer": numpy.mean(layer_latencies) / 1e6,
        "qkv": numpy.mean(qkv_latencies) / 1e6,
        "attn": numpy.mean(attn_latencies) / 1e6,
        "o": numpy.mean(o_latencies) / 1e6,
        "ffn": numpy.mean(ffn_latencies) / 1e6,
    }

    def _get_mean_metric_value(values: List[torch.Tensor]):
        tmp = torch.cat(values)
        return (torch.sum(tmp) / tmp.numel()).item()

    agg_bws = {
        "qkv": _get_mean_metric_value(qkv_bws),
        "attn": _get_mean_metric_value(attn_bws),
        "o": _get_mean_metric_value(o_bws),
        "ffn": _get_mean_metric_value(ffn_bws),
    }

    agg_comps = {
        "qkv": _get_mean_metric_value(qkv_comps),
        "attn": _get_mean_metric_value(attn_comps),
        "o": _get_mean_metric_value(o_comps),
        "ffn": _get_mean_metric_value(ffn_comps),
    }

    stats = {
        "latency": agg_latencies,
        "bws": agg_bws,
        "comps": agg_comps
    }

    return prompt_len, stats

def get_data(args):
    TestReportKernelPoints = PrefillKernelPoints
    trace_info = ReportKernelPoints(
        layer=TestReportKernelPoints.layer_line_num,
        qkv=TestReportKernelPoints.qkv_line_num,
        attn=TestReportKernelPoints.attn_line_num,
        o=TestReportKernelPoints.o_line_num,
        ffn1=TestReportKernelPoints.ffn1_line_num,
        ffn2=TestReportKernelPoints.ffn2_line_num
    )
    # res = get_kernel_stats('plots/1_1_prefill_util/1_1_prefill_util_latest/report_1_1_prefill_util_1280_2025-01-10_23-34-40.sqlite', trace_info, split_tag='1_1_prefill_util_')
    # print(">>>>>>> res: ", res)
    kernel_lists = ['qkv', 'attn', 'o', 'ffn']
    data: dict[str, dict[str, list]] = {
        "latency": {},
        "bws": {},
        "comps": {}
    }
    for kernel in kernel_lists:
        data['latency'][kernel] = []
        data['bws'][kernel] = []
        data['comps'][kernel] = []

    results = []
    for rpath in glob.glob(f"{args.reports_dir}/*.sqlite"):
        res = get_kernel_stats(rpath, trace_info, split_tag=args.split_tag)
        print(">>>>>>> res: ", res)
        results.append(res)

    results = sorted(results, key=lambda x: x[0])
    prompt_lens = []
    for prompt_len, stats in results:
        prompt_lens.append(prompt_len)
        layer_latency = stats["latency"]["layer"]
        for kernel in kernel_lists:
            data["latency"][kernel].append(stats["latency"][kernel]/layer_latency*100)
            data["bws"][kernel].append(stats["bws"][kernel])
            # data["comps"][kernel].append(stats["comps"][kernel])
            if kernel == "qkv":
                tflops = get_gemm_compute_flops(LLAMA2_7B, ModelOperator.QKV_PROJ, prompt_len) / 1e12  # TFLOPS
            elif kernel == "attn":
                tflops = get_attn_compute_flops(LLAMA2_7B, prompt_len, prompt_len) / 2 / 1e12  # TFLOPS
                print(f"{LLAMA2_7B.hidden_size}")
            elif kernel == "o":
                tflops = get_gemm_compute_flops(LLAMA2_7B, ModelOperator.O_PROJ, prompt_len) / 1e12  # TFLOPS
            elif kernel == "ffn":
                tflops = (
                    get_gemm_compute_flops(LLAMA2_7B, ModelOperator.FFN0, prompt_len)
                    + get_gemm_compute_flops(LLAMA2_7B, ModelOperator.FFN1, prompt_len)
                ) / 1e12  # TFLOPS
            latency_s = stats["latency"][kernel] / 1e3  # s
            compute_stat = tflops / latency_s
            compute_pct = compute_stat / A100_80GB_META_DATA.PEAK_FP16_TFLOPS * 100
            data["comps"][kernel].append(compute_pct)

    print(f'prompt_lens: {prompt_lens}')
    print(f'data: {data}')
    return prompt_lens, data

def plot_kernel_util_small(target_workloads, workload_names, data, schemes, category, ylabel, save_path):
    fig, ax = plt.subplots(figsize=PlotConfig.figsize)

    x = numpy.arange(len(schemes)) # the label locations
    width = PlotConfig.bar_width  # the width of the bars
    offsets = numpy.arange(len(target_workloads)) * width - width * (len(target_workloads) - 1) / 2
    offsets = offsets * PlotConfig.bar_padding # add spaces for bars in a group
    logging.info(offsets)

    plot_with_legend = 'legend' in save_path

    group_labels = []
    for i in range(len(target_workloads)):
        scheme = schemes[i]
        tag = target_workloads[i]
        print(scheme, tag)
        group_labels.append(scheme_to_label[scheme])

        # plot data for target workloads
        idx = workload_names.index(tag)
        target_value = [
            data[category][scheme][idx] for scheme in schemes
        ]

        label = f'Len: {tag}'

        plt.bar(x + offsets[i], target_value, width,
            label=label, color=PlotConfig.colors[i], edgecolor='black', linewidth=0.5)

    plt.xticks(x, group_labels)

    # plt.xlabel('Prompt length')
    plt.ylabel(ylabel)

    plt.ylim([0, 100])

    plt.grid(axis='y', color='lightgray', linestyle='--')
    ax.set_axisbelow(True)

    if plot_with_legend:
        fig.legend(ncol=4, loc='upper center', frameon=False)
        fig.subplots_adjust(top=0.75)

    # plt.show()
    plt.savefig(save_path, bbox_inches='tight')
    logging.info('saved in ' + save_path)

def plot_kernel_mem_util_small(target_workloads, workload_names, data, schemes, save_path, plot_time=False):
    fig, ax1 = plt.subplots(figsize=PlotConfig.figsize)
    if plot_time:
        ax2 = ax1.twinx()

    x = numpy.arange(len(schemes)) # the label locations
    width = PlotConfig.bar_width  # the width of the bars
    offsets = numpy.arange(len(target_workloads)) * width - width * (len(target_workloads) - 1) / 2
    offsets = offsets * PlotConfig.bar_padding # add spaces for bars in a group
    logging.info(offsets)

    group_labels = []
    for i in range(len(schemes)):
        scheme = schemes[i]
        tag = target_workloads[i]
        print(scheme, tag)
        group_labels.append(scheme_to_label[scheme])

        # plot data for target workloads
        idx = workload_names.index(tag)
        target_value = [
            data['bws'][scheme][idx] for scheme in schemes
        ]

        plt.bar(x + offsets[i], target_value, width,
            label=tag, color=PlotConfig.colors[i], edgecolor='black', linewidth=0.5)

    if plot_time:
        for i, workload in enumerate(target_workloads):
            x_values = [o + x[i] for o in offsets]
            y_idx = workload_names.index(workload)
            y_values = [data['latency'][scheme][y_idx] for scheme in schemes]

            ax2.plot(x_values, y_values, marker='D', markersize=PlotConfig.markersize,
                    markerfacecolor='none', markeredgewidth=PlotConfig.markeredgewidth,
                    color='black', lw=0.5)
        ax2.set_ylabel('Execute Time Ratio (%)', color='red')
        ax2.yaxis.label.set_color('black')
        ax2.set_ylim([0, 100])
        ax2.set_axisbelow(True)

    plt.xticks(x, group_labels)

    # ax1.set_xlabel('Prompt length')
    ax1.set_ylabel('Mem BW Util. (%)')


    ax1.set_ylim([0, 100])

    # ax1.legend(group_labels, ncol=4, loc='upper center', fontsize=9)
    plt.grid(axis='y', color='lightgray', linestyle='--')
    ax1.set_axisbelow(True)

    # plt.show()
    plt.savefig(save_path, bbox_inches='tight')
    logging.info('saved in ' + save_path)

def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--reports-dir", type=str, default="evaluation/results/3_1_prefill_kernel")
    parser.add_argument("--split-tag", type=str, default="3_1_prefill_kernel_")
    parser.add_argument("--use-existed-data", action="store_true")
    parser.add_argument("--no-crop-margin", action="store_true")
    parser.add_argument(
        "--output-dir",
        type=str,
        default="evaluation/plots/3_1_prefill_kernel",
    )
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    if args.use_existed_data:
        # 0320, range(0, 4097, 256), llama-2 7B
        prompt_lens = [256, 512, 768, 1024, 1280, 1536, 1792, 2048, 2304, 2560, 2816, 3072, 3328, 3584, 3840, 4096]
        data = {'latency': {'qkv': [10.13177611598924, 16.469358894232858, 21.946163774352947, 20.678981463833264, 22.482816806935553, 22.221143947035326, 21.54327814878419, 21.4046091979262, 20.85047492134555, 21.48352428462451, 20.975713460612823, 20.749200179300463, 21.066188986375888, 20.523076534372517, 20.78317669425087, 20.500979249472874], 'attn': [5.528663459102531, 4.1289253468079945, 4.907357815272779, 4.889876411339333, 5.950786784144848, 6.021095077236286, 6.608363777608204, 7.037412994606802, 7.537191205266502, 8.117731466345715, 8.31718066598933, 8.756674327952854, 9.379598848243756, 9.785902741579124, 10.33447148136531, 10.879313272457209], 'o': [4.804140978289967, 6.380822756748038, 7.218203649879822, 7.340014309817053, 6.843615655913295, 7.169512193709693, 6.646278942450764, 6.4576694240286, 6.640702531217116, 6.3371420458276235, 6.552569352572414, 6.577662809655855, 6.351883782276242, 6.53614538065959, 6.207813583670636, 6.38428617823989], 'ffn': [29.2818776045219, 45.272172190253926, 55.547001073275936, 56.84848751269988, 54.19922777686037, 54.64645248106874, 55.6484611432163, 55.49229897770612, 55.23871426924949, 54.40241991957666, 54.77054463136618, 54.58674099432655, 53.9493590035104, 54.05469739938883, 53.501229108958185, 53.1299549776042]}, 'bws': {'qkv': [41.32258224487305, 30.627119064331055, 33.4945068359375, 20.40833282470703, 29.092105865478516, 26.103824615478516, 28.677570343017578, 17.60240936279297, 22.747211456298828, 21.50666618347168, 20.262840270996094, 19.25619888305664, 21.5089054107666, 21.378313064575195, 20.313199996948242, 17.621339797973633], 'attn': [2.0, 5.357142925262451, 15.571428298950195, 14.964285850524902, 14.609756469726562, 15.100000381469727, 14.015151977539062, 13.662651062011719, 12.949495315551758, 13.052173614501953, 12.835821151733398, 12.242037773132324, 11.876404762268066, 11.495049476623535, 11.18061637878418, 10.426356315612793], 'o': [35.79999923706055, 28.39130401611328, 23.80645179748535, 22.16666603088379, 24.10416603088379, 22.483333587646484, 20.884057998657227, 24.3150691986084, 27.21839141845703, 23.27777862548828, 25.89215660095215, 23.786325454711914, 26.94871711730957, 26.893939971923828, 28.0, 25.91216278076172], 'ffn': [40.58241653442383, 33.43902587890625, 32.54585266113281, 22.290908813476562, 27.96143341064453, 20.755056381225586, 23.109289169311523, 22.200000762939453, 24.266016006469727, 20.841415405273438, 20.219287872314453, 19.209375381469727, 22.265024185180664, 20.927536010742188, 20.7424373626709, 18.380531311035156]}, 'comps': {'qkv': [76.58064270019531, 81.44068145751953, 79.96703338623047, 80.35832977294922, 79.55921173095703, 79.16393280029297, 78.84111785888672, 77.91566467285156, 80.95539093017578, 80.71666717529297, 80.39274597167969, 80.03856658935547, 80.20864868164062, 81.68433380126953, 81.23713684082031, 81.10041809082031], 'attn': [4.058823585510254, 15.357142448425293, 27.66666603088379, 31.89285659790039, 34.85365676879883, 40.29999923706055, 41.10606002807617, 43.21686935424805, 44.858585357666016, 47.747825622558594, 49.38059616088867, 49.866241455078125, 51.5224723815918, 52.658416748046875, 53.66079330444336, 53.437984466552734], 'o': [51.93333435058594, 68.13043212890625, 76.38710021972656, 74.85713958740234, 84.4375, 80.80000305175781, 88.07246398925781, 87.6986312866211, 82.97701263427734, 89.68888854980469, 87.1568603515625, 82.86325073242188, 89.8974380493164, 85.51515197753906, 91.04511260986328, 87.0], 'ffn': [71.35164642333984, 79.06707000732422, 85.31440734863281, 85.76969909667969, 89.6942138671875, 87.96853637695312, 87.0273208618164, 80.82635498046875, 81.56684875488281, 85.23853302001953, 82.25373077392578, 81.3687515258789, 83.44039154052734, 82.57699584960938, 84.49179077148438, 83.80450439453125]}}
    else:
        prompt_lens, data = get_data(args)

    targeted_prompt_lens = [512, 1024, 2048, 4096]
    scheme_order = ['qkv', 'attn', 'o', 'ffn']
    plot_kernel_util_small(targeted_prompt_lens, prompt_lens, data, scheme_order, 'comps', 'Comp. Util. (%)', f'{args.output_dir}/legend_kernel_util.pdf')
    plot_kernel_util_small(targeted_prompt_lens, prompt_lens, data, scheme_order, 'comps', 'Comp. Util. (%)', f'{args.output_dir}/prefill_kernel_comp_util.pdf')
    plot_kernel_mem_util_small(targeted_prompt_lens, prompt_lens, data, scheme_order, f'{args.output_dir}/prefill_kernel_bw_util.pdf')

    if not args.no_crop_margin:
        output_path = crop_margins(
            f"{args.output_dir}/legend_kernel_util.pdf", "-p4 10 -1800 10 10"
        )
        print(f"Croped to {output_path} successfully.")
        output_path = crop_margins(f"{args.output_dir}/prefill_kernel_comp_util.pdf")
        print(f"Croped to {output_path} successfully.")
        output_path = crop_margins(f"{args.output_dir}/prefill_kernel_bw_util.pdf")
        print(f"Croped to {output_path} successfully.")
