import argparse
import itertools
import matplotlib
import numpy

from matplotlib import pyplot
from evaluation.utils.output_parser import (
    parse_num_blocks,
    parse_timestamp,
)
from evaluation.plots.utils.plot_utils import SYS_NAME

matplotlib.rcParams['font.family'] = 'Arial'
matplotlib.rcParams['pdf.fonttype'] = 42
matplotlib.rcParams['ps.fonttype'] = 42
matplotlib.rcParams['font.size'] = 10
matplotlib.rcParams['axes.titlesize'] = 10
matplotlib.rcParams['axes.labelsize'] = 10
matplotlib.rcParams['xtick.labelsize'] = 10
matplotlib.rcParams['ytick.labelsize'] = 10
matplotlib.rcParams['legend.fontsize'] = 10

scheme_to_label = {
    'baseline': 'Baseline',
    'ours': 'Load-aware'
}

def get_hbm_usage_for_path(path: str, remove_warmup: bool = False):
    with open(path) as f:
        lines = f.readlines()
    block_lines = [line for line in lines if "record blocks" in line]
    num_blocks = [parse_num_blocks(line) for line in block_lines]
    timestamps = [parse_timestamp(line) for line in block_lines]
    if remove_warmup:
        idx = num_blocks.index(0)
        assert len(num_blocks) == len(timestamps)
        num_blocks = num_blocks[idx:]
        timestamps = timestamps[idx:]
    return num_blocks, timestamps


def group_by_bin_edges(timestamp: float, pre_edges: list[float], post_edges: list[float]):
    for i, (prev, post) in enumerate(zip(pre_edges, post_edges)):
        if prev <= timestamp <= post:
            return i
    return i


def calc_avg_usage(
    stats: list[tuple[float, int]],
    last_block: int,
    prev_edge: float,
    post_edge: float,
):
    total_blocks = 0
    prev_timestamp = prev_edge
    prev_blocks = last_block
    for (timestamp, num_blocks) in stats:
        total_blocks += prev_blocks * (timestamp - prev_timestamp)
        prev_timestamp = timestamp
        prev_blocks = num_blocks
    total_blocks += prev_blocks * (post_edge - prev_timestamp)
    total_time = post_edge - prev_edge
    return total_blocks / total_time


def get_grouped_last_blocks(
    grouped_stats: dict[int, list[tuple[float, int]]], num_groups: int
):
    last_blocks = [0]
    for i in range(num_groups):
        if i not in grouped_stats:
            last_block = last_blocks[i]
        else:
            last_block = grouped_stats[i][-1][1]
        last_blocks.append(last_block)
    return last_blocks


def get_bins_block_usage(stats: list[tuple[float, int]], bin_edges: list[float]):
    pre_edges = bin_edges[:-1]
    post_edges = bin_edges[1:]
    grouped_stats = {
        key: list(stats) for key, stats in itertools.groupby(
            stats, key=lambda stat: group_by_bin_edges(stat[0], pre_edges, post_edges)
        )
    }
    num_bins = len(bin_edges) - 1
    last_blocks = get_grouped_last_blocks(grouped_stats, num_bins)
    bins_stat = [
        grouped_stats[i] if i in grouped_stats else [] for i in range(num_bins)
    ]
    grouped_avg_usages = [
        calc_avg_usage(stat, last_block, prev_edge, post_edge)
        for stat, last_block, prev_edge, post_edge in zip(
            bins_stat, last_blocks, pre_edges, post_edges
        )
    ]
    return grouped_avg_usages


def plot_hbm_usage_for_each_iter(prefill_path: str, decode_path: str, output_path: str):
    prefill_blocks, prefill_timestamps = get_hbm_usage_for_path(prefill_path)
    decode_blocks, decode_timestamps = get_hbm_usage_for_path(decode_path)
    pyplot.step(prefill_timestamps, prefill_blocks, where="post", label="prefill")
    pyplot.step(decode_timestamps, decode_blocks, where="post", label="decode")
    pyplot.savefig(output_path)
    pyplot.clf()

def process_timestamps(bin_edges: list[float]) -> list[float]:
    start_ts = bin_edges[0]
    return [t - start_ts for t in bin_edges[:-1]]

def process_used_hbm(hbm_data: list[float]) -> list[float]:
    # convert to HBM ratios
    mb_per_token = 0.5
    num_token_per_block = 16

    # llama-2 7B, A100, decode instance
    # INFO 02-26 09:03:41 model_runner.py:1778] Loading model weights took 12.5523 GB
    # INFO 02-26 09:03:42 worker.py:244] Memory profiling results: total_gpu_memory=79.32GiB initial_memory_usage=13.27GiB peak_torch_memory=13.20GiB memory_usage_post_profile=13.57Gib non_torch_memory=0.75GiB kv_cache_size=49.50GiB gpu_memory_utilization=0.80
    others = 13.57 * 1.073741824
    A100_HBM_GB = 80

    return [
        (x * mb_per_token * num_token_per_block / 1024 + others) / A100_HBM_GB * 100
        for x in hbm_data
    ]

def plot_disagg_hbm_usage(
    prefill_path: str, decode_path: str, output_path: str, num_bins: int = 100
):
    prefill_blocks, prefill_timestamps = get_hbm_usage_for_path(prefill_path, remove_warmup=True)
    decode_blocks, decode_timestamps = get_hbm_usage_for_path(decode_path, remove_warmup=True)
    min_timestamp = min(min(prefill_timestamps), min(decode_timestamps))
    max_timestamp = max(max(prefill_timestamps), max(decode_timestamps))
    bin_width = (max_timestamp - min_timestamp) / num_bins
    bin_edges = [min_timestamp + i * bin_width for i in range(num_bins + 1)]

    prefill_stats = list(zip(prefill_timestamps, prefill_blocks))
    decode_stats = list(zip(decode_timestamps, decode_blocks))
    grouped_prefill_usage = get_bins_block_usage(prefill_stats, bin_edges)
    grouped_decode_usage = get_bins_block_usage(decode_stats, bin_edges)

    print(f'{bin_edges=}')
    print(f'{grouped_prefill_usage=}')
    print(f'{grouped_decode_usage=}')

    # pyplot.step(bin_edges[:-1], grouped_prefill_usage, label="prefill")
    # pyplot.step(bin_edges[:-1], grouped_decode_usage, label="decode")
    # pyplot.legend()
    # pyplot.savefig(output_path)

    x_times = process_timestamps(bin_edges)
    prefill_hbm_ratio = process_used_hbm(grouped_prefill_usage)
    decode_hbm_ratio = process_used_hbm(grouped_decode_usage)

    colors = ['#0000ED', '#BF0000', '#006521', '#6500CC']

    fig, ax = pyplot.subplots(figsize=(2.8, 2.2))
    # fig, ax = pyplot.subplots(figsize=(3.2, 2))

    print(f'{prefill_hbm_ratio=}')
    print(f'{decode_hbm_ratio=}')

    ax.step(x_times, prefill_hbm_ratio, color=colors[0], label="Prefill")
    ax.step(x_times, decode_hbm_ratio, color=colors[2], linestyle='--', label="Decoding")

    # 填充 x < 22 的范围背景为灰色
    pyplot.axvspan(0, 22, color='gray', alpha=0.3)  # alpha 控制透明度


    # 使用空心圆标注特定点 (x=3, y=5)
    pyplot.scatter([1], [19], marker='o', facecolors='none', edgecolors='black', s=40, linewidths=1)
    # 添加注释
    pyplot.annotate("Model weights loaded",
                    xy=(1, 19),  # 注释的坐标位置
                    xytext=(3.5, 5),  # 文本的显示位置
                    arrowprops=dict(facecolor='black', arrowstyle="->"))  # 箭头样式

    pyplot.text(3, 68, "Warmup")

    ax.set_xlabel('Time (s)')
    ax.set_ylabel('HBM Capacity Util. (%)')

    pyplot.xlim([0, 65])
    pyplot.ylim([0, 80])

    fig.legend(loc=(0.5, 0.5), frameon=False, markerfirst=False)
    pyplot.savefig(output_path, bbox_inches='tight')
    pyplot.clf()


def plot_offload_hbm_usage(
    prefill_path: str,
    decode_path: str,
    offload_path: str,
    output_path: str,
    num_bins: int = 100,
):
    prefill_blocks, prefill_timestamps = get_hbm_usage_for_path(prefill_path, remove_warmup=True)
    decode_blocks, decode_timestamps = get_hbm_usage_for_path(decode_path, remove_warmup=True)
    offload_blocks, offload_timestamps = get_hbm_usage_for_path(offload_path)

    min_timestamp = min(
        min(prefill_timestamps), min(decode_timestamps), min(offload_timestamps)
    )
    max_timestamp = max(
        max(prefill_timestamps), max(decode_timestamps), max(offload_timestamps)
    )
    bin_width = (max_timestamp - min_timestamp) / num_bins
    bin_edges = [min_timestamp + i * bin_width for i in range(num_bins + 1)]

    prefill_stats = list(zip(prefill_timestamps, prefill_blocks))
    decode_stats = list(zip(decode_timestamps, decode_blocks))
    offload_stats = list(zip(offload_timestamps, offload_blocks))
    grouped_prefill_usage = get_bins_block_usage(prefill_stats, bin_edges)
    grouped_decode_usage = get_bins_block_usage(decode_stats, bin_edges)
    grouped_offload_usage = get_bins_block_usage(offload_stats, bin_edges)
    pyplot.step(bin_edges[:-1], grouped_prefill_usage, label="prefill")
    pyplot.step(bin_edges[:-1], grouped_decode_usage, label="decode")
    pyplot.step(bin_edges[:-1], grouped_offload_usage, label="offload")
    pyplot.legend()
    pyplot.savefig(output_path)
    pyplot.clf()


def plot_hbm_usage_offload_vs_disagg(
    disagg_prefill_path: str,
    offload_prefill_path: str,
    offload_attn_path: str,
    output_path: str,
    num_bins: int = 100,
):
    disagg_prefill_blocks, disagg_prefill_timestamps = get_hbm_usage_for_path(
        disagg_prefill_path, remove_warmup=True
    )
    disagg_start = min(disagg_prefill_timestamps)
    disagg_prefill_times = [time - disagg_start for time in disagg_prefill_timestamps]
    offload_prefill_blocks, offload_prefill_timestamps = get_hbm_usage_for_path(
        offload_prefill_path, remove_warmup=True
    )
    offload_attn_blocks, offload_attn_timestamps = get_hbm_usage_for_path(
        offload_attn_path
    )
    offload_start = min(min(offload_prefill_timestamps), min(offload_attn_timestamps))
    offload_prefill_times = [time - offload_start for time in offload_prefill_timestamps]
    offload_attn_times = [time - offload_start for time in offload_attn_timestamps]
    min_timestamp = min(
        min(offload_prefill_times), min(disagg_prefill_times), min(offload_attn_times)
    )
    max_timestamp = max(
        max(offload_prefill_times), max(disagg_prefill_times), max(offload_attn_times)
    )
    bin_width = (max_timestamp - min_timestamp) / num_bins
    bin_edges = [min_timestamp + i * bin_width for i in range(num_bins + 1)]

    disagg_prefill_stats = list(zip(disagg_prefill_times, disagg_prefill_blocks))
    offload_prefill_stats = list(zip(offload_prefill_times, offload_prefill_blocks))
    offload_attn_stats = list(zip(offload_attn_times, offload_attn_blocks))
    disagg_prefill_usage = get_bins_block_usage(disagg_prefill_stats, bin_edges)
    offload_prefill_usage = get_bins_block_usage(offload_prefill_stats, bin_edges)
    offload_attn_usage = get_bins_block_usage(offload_attn_stats, bin_edges)
    offload_prefill_usage_with_attn = [
        prefill + offload
        for prefill, offload in zip(offload_prefill_usage, offload_attn_usage)
    ]

    print(f'{bin_edges=}')
    print(f'{disagg_prefill_usage=}')
    print(f'{offload_prefill_usage_with_attn=}')

    # pyplot.step(bin_edges[:-1], disagg_prefill_usage, label="disagg")
    # pyplot.step(bin_edges[:-1], offload_prefill_usage_with_attn, label="offload")
    # pyplot.legend()
    # pyplot.savefig(output_path)
    # pyplot.clf()

    x_times = process_timestamps(bin_edges)
    disagg_prefill_use_ratio = process_used_hbm(disagg_prefill_usage)
    offload_prefill_with_attn_use_ratio = process_used_hbm(offload_prefill_usage_with_attn)

    colors = ['#0000ED', '#BF0000', '#006521', '#6500CC']

    fig, ax = pyplot.subplots(figsize=(2.8, 2.2))
    # fig, ax = pyplot.subplots(figsize=(3.2, 2))

    ax.step(x_times, disagg_prefill_use_ratio, color=colors[0], label="vLLM")
    ax.step(x_times, offload_prefill_with_attn_use_ratio, color=colors[1], linestyle='--', label=SYS_NAME)

    # 填充 x < 22 的范围背景为灰色
    pyplot.axvspan(0, 40, color='gray', alpha=0.3)  # alpha 控制透明度
    pyplot.text(4, 68, "Warmup")

    # 使用空心圆标注特定点 (x=3, y=5)
    pyplot.scatter([1], [19], marker='o', facecolors='none', edgecolors='black', s=40, linewidths=1)
    # 添加注释
    pyplot.annotate("Model weights loaded",
                    xy=(1, 19),  # 注释的坐标位置
                    xytext=(3.5, 5),  # 文本的显示位置
                    arrowprops=dict(facecolor='black', arrowstyle="->"))  # 箭头样式

    pyplot.grid(axis='y', color='lightgray', linestyle='--', linewidth=0.25)
    pyplot.grid(axis='x', color='lightgray', linestyle='--', linewidth=0.25)

    ax.set_xlabel('Time (s)')
    ax.set_ylabel('HBM Capacity Util. (%)')

    pyplot.xlim([0, 100])
    pyplot.ylim([0, 80])

    fig.legend(loc=(0.5, 0.42), frameon=False, markerfirst=False)
    pyplot.savefig(output_path, bbox_inches='tight')

def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--result-dir", type=str, default="evaluation/results/hbm_usage")
    parser.add_argument("--output-dir", type=str, default="evaluation/plots/hbm_usage")
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    plot_disagg_hbm_usage(
        f"{args.result_dir}/disagg_prefill.out",
        f"{args.result_dir}/disagg_decode.out",
        f"{args.output_dir}/disagg_hbm_usage.pdf",
    )
    plot_offload_hbm_usage(
        f"{args.result_dir}/offload_prefill.out",
        f"{args.result_dir}/offload_decode.out",
        f"{args.result_dir}/offload_offload.out",
        f"{args.output_dir}/offload_hbm_usage.pdf",
    )
    plot_hbm_usage_offload_vs_disagg(
        f"{args.result_dir}/disagg_prefill.out",
        f"{args.result_dir}/offload_prefill.out",
        f"{args.result_dir}/offload_offload.out",
        f"{args.output_dir}/hbm_usage_comparison.pdf",
    )
