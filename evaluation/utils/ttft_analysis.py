import argparse
import dataclasses
import pprint

from evaluation.utils.output_parser import (
    parse_request_id,
    parse_step_time,
    parse_scheduled_request_ids,
    parse_is_recv_fin,
    parse_step_start,
    parse_step_stop,
)


@dataclasses.dataclass
class PrefillTimestamp:
    start: float
    end: float


def record_prefill_step_time_for_d_inst(prefill_step_lines: list[str]):
    pass


def parse_prefill_inst_step_time(result_dir: str):
    with open(f"{result_dir}/prefill.out") as f:
        lines = f.readlines()

    req_lines = [line.strip() for line in lines if "Added request" in line]
    requests_id = [parse_request_id(line) for line in req_lines]
    step_lines = [line.strip() for line in lines if "step_time" in line]
    prefill_step_times = [parse_step_time(line) for line in step_lines]
    p_inst_step_times = dict(zip(requests_id, prefill_step_times))
    return p_inst_step_times


def parse_decode_inst_prefill_time(result_dir: str):
    with open(f"{result_dir}/decode.out") as f:
        lines = f.readlines()
    prefill_step_lines = [line.strip() for line in lines if "prefill step" in line]

    prefill_timestamp: dict[str, PrefillTimestamp] = {}
    for line in prefill_step_lines:
        request_ids_in_step = parse_scheduled_request_ids(line)
        is_recv_fin = parse_is_recv_fin(line)
        if not is_recv_fin:
            step_start = parse_step_start(line)
            for req_id in request_ids_in_step:
                assert req_id not in prefill_timestamp, req_id
                prefill_timestamp[req_id] = PrefillTimestamp(
                    start=step_start, end=None
                )
        else:
            step_end = parse_step_stop(line)
            for req_id in request_ids_in_step:
                assert req_id in prefill_timestamp, req_id
                assert prefill_timestamp[req_id].end is None
                prefill_timestamp[req_id].end = step_end

    return {
        req_id: (prefill.end - prefill.start) * 1000
        for req_id, prefill in prefill_timestamp.items()
    }


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--result-dir",
        type=str,
        # default="outputs/e2e_perf/disagg/meta-llama/Llama-2-13b-hf/open-thoughts-qps_3-numreqs_30-2025-03-09_16-22-08",
        default="outputs/e2e_perf/meta-llama/Llama-2-7b-hf/sharegpt-qps_4-numreqs_10-2025-03-22_17-59-18",
    )
    parser.add_argument("--output-dir", type=str, default=None)
    args = parser.parse_args()
    if args.output_dir is None:
        args.output_dir = args.result_dir
    return args


if __name__ == "__main__":
    args = parse_args()
    p_inst_step_times = parse_prefill_inst_step_time(args.result_dir)
    pprint.pprint(p_inst_step_times)

    d_inst_prefill_times = parse_decode_inst_prefill_time(args.result_dir)
    pprint.pprint(d_inst_prefill_times)
