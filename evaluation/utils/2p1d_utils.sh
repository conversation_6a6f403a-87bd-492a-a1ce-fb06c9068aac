#!/bin/bash
set -e

# shellcheck source=../../examples/adrenaline/2p1d/adrenaline_instances_start.sh
source examples/adrenaline/2p1d/adrenaline_instances_start.sh
# shellcheck source=../../examples/distributed_kv/2p1d/disagg_prefill_instances_start.sh
source examples/distributed_kv/2p1d/disagg_prefill_instances_start.sh
# shellcheck source=../../evaluation/utils/client_utils.sh
source evaluation/utils/client_utils.sh

function run_offload_profile() {
    OFFLOAD_RATIO=${OFFLOAD_RATIO:=0.6}
    local num_execs=${1:-"1"}
    echo "Profile offload with NUM_REQS=${NUM_REQS}, REQ_RATE=${REQ_RATE}, num_execs=${num_execs}"
    local cnt=0
    while ((cnt < num_execs)); do
        start_adrenaline_environemnt
        RUN_TYPE=offload/2p1d DECODE_FILE=d0.out start_client
        wait_for_client_running
        local pid
        pid=$(pgrep -f bench_dataset_serving)
        if wait_for_client_fin; then
            if wait "$pid"; then
                cnt=$((cnt + 1))
                echo "Client ($cnt) finished."
            else
                echo "Client failed."
            fi
        else
            pkill -f benchmark_serving
            wait_for_client_fin
        fi
        stop_adrenaline_environment
    done
}

function run_disagg_profile() {
    if [[ -n "${OFFLOAD_RATIO}" ]]; then
        echo "${OFFLOAD_RATIO} should not be set for disagg inference mode"
        return 1
    fi
    local num_execs=${1:-"1"}
    echo "Profile disagg with NUM_REQS=${NUM_REQS}, REQ_RATE=${REQ_RATE}, num_execs=${num_execs}"
    local cnt=0
    while ((cnt < num_execs)); do
        start_disagg_environemnt
        RUN_TYPE=disagg/2p1d DECODE_FILE=d0.out start_client
        wait_for_client_running
        local pid
        pid=$(pgrep -f bench_dataset_serving)
        if wait_for_client_fin; then
            if [[ $(wait "$pid") -eq 0 ]]; then
                cnt=$((cnt + 1))
                echo "Client ($cnt) finished."
            else
                echo "Client failed."
            fi
        else
            pkill -f benchmark_serving
            wait_for_client_fin
        fi
        stop_disagg_environment
    done
}
