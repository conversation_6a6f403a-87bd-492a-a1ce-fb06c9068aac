#!/bin/bash
set -e

# shellcheck source=../../evaluation/utils/cross_node_utils.sh
source evaluation/utils/cross_node_utils.sh

function evaluate_remote_disagg_with_sharegpt() {
    NUM_REQS=250
    DATASET_NAME=sharegpt
    local req_rates=(
        1 2 3 4 5
    )
    local req_rate
    for req_rate in "${req_rates[@]}"; do
        REQ_RATE=${req_rate} run_disagg_profile 1
        OFFLOAD_RATIO=0.4 REQ_RATE=${req_rate} run_offload_profile 1
        OFFLOAD_RATIO=0.5 REQ_RATE=${req_rate} run_offload_profile 1
        OFFLOAD_RATIO=0.6 REQ_RATE=${req_rate} run_offload_profile 1
        OFFLOAD_RATIO=0.7 REQ_RATE=${req_rate} run_offload_profile 1
    done
}

evaluate_remote_disagg_with_sharegpt
