#!/bin/bash
set -e

CUR_DIR=$(dirname "$0")
RESULT_DIR="${CUR_DIR}/results/ttft_sm_study"

function prof_prefill_latency() {
    if [[ ! -d "${RESULT_DIR}" ]]; then
        mkdir -p "${RESULT_DIR}"
    fi

    time_str=$(date +"%Y-%m-%d_%H-%M-%S")
    stat_path="${RESULT_DIR}/prefill_latency_${time_str}.txt"

    local input_lens=(512 1024 4096)
    local sm_ratios=(50 60 70 80 90 100)

    for input_len in "${input_lens[@]}"; do
        for sm_ratio in "${sm_ratios[@]}"; do
            local i=1
            echo ">>> ${input_len} ${sm_ratio}" >> ${stat_path}
            while [ $i -le 5 ]; do
                python -m benchmarks.offload_benchmarks.benchmark_prefill_with_sm  \
                --input-len "${input_len}" --prefill-sm "${sm_ratio}"
                cat prefill_sm_timer.txt >> ${stat_path}
                echo "" >> ${stat_path}
                let "i++"
                sleep 2
            done
        done
    done
}

prof_prefill_latency
