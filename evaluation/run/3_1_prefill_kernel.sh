#!/bin/bash

set -e

CUR_DIR=$(dirname "$0")
RESULT_ROOT_DIR=${CUR_DIR}/../results
EVALUATE_NAME=$(basename "$0" .sh)
RESULT_DIR=${RESULT_ROOT_DIR}/${EVALUATE_NAME}

# key args
# PROMPT_LENS=(256 512 ... 4096)
readarray -t PROMPT_LENS < <(seq 256 256 4096)
OUTPUT_LEN=8
DECODE_BATCH=1
MODEL="meta-llama/Llama-2-7b-hf"

mkdir -p ${RESULT_ROOT_DIR}
mkdir -p ${RESULT_DIR}

do_profiling() {
    echo "start do_profiling..."
    local arg_input_len=$1
    local arg_profile_out_path=$2
    nsys profile \
        --gpu-metrics-device=0 \
        --gpu-metrics-frequency=100000 \
        --capture-range=cudaProfilerApi \
        --capture-range-end=stop \
        --output ${arg_profile_out_path} \
        python -m benchmarks.disagg_benchmarks.bench_disagg_metrics \
        --input-len ${arg_input_len} \
        --output-len ${OUTPUT_LEN} \
        --decode-batch ${DECODE_BATCH} \
        --model ${MODEL} \
        --enforce-eager
    sleep 5
}

for len in "${PROMPT_LENS[@]}"; do
    time_str=$(date +"%Y-%m-%d_%H-%M-%S")
    report_path="${RESULT_DIR}/report_${EVALUATE_NAME}_${len}_${time_str}"
    do_profiling ${len} ${report_path}
done

make -j -f evaluation/run/Makefile REP_DIR="${RESULT_DIR}"
