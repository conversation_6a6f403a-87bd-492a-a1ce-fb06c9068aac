#!/bin/bash
set -e

CUR_DIR=$(dirname "$0")
RESULT_ROOT_DIR=${CUR_DIR}/../results
EVALUATE_NAME=$(basename "$0" .sh)
REP_DIR=${RESULT_ROOT_DIR}/${EVALUATE_NAME}

function profile_offload_overhead_with_diff_offload_pct() {
    mkdir -p "${REP_DIR}"

    local input_len=1024
    local batch=32
    local offload_pcts=(0 0.1 0.2 0.3 0.4 0.5 0.6 0.8 1)
    for offload_pct in "${offload_pcts[@]}"; do
        nsys profile \
            --gpu-metrics-devices=0,1 \
            --gpu-metrics-frequency=100000 \
            --capture-range=cudaProfilerApi \
            --capture-range-end=stop \
            -o "${REP_DIR}"/offload_overhead_offload_pct_"${offload_pct}"_input_len_"${input_len}"_batch_"${batch}".nsys-rep \
            -f true \
            python -m benchmarks.offload_benchmarks.benchmark_offload_metrics_with_offload_pct \
            --input-len "${input_len}" \
            --decode-batch "${batch}" \
            --offload-pct "${offload_pct}"
    done
}

function profile_offload_overhead_with_diff_offload_ratio() {
    mkdir -p "${REP_DIR}"

    local input_len=1024
    local num_locals=32
    local offload_ratios=(0 0.2 0.4 0.6 0.8 1 1.2)
    for offload_ratio in "${offload_ratios[@]}"; do
        nsys profile \
            --gpu-metrics-devices=0,1 \
            --gpu-metrics-frequency=100000 \
            --capture-range=cudaProfilerApi \
            --capture-range-end=stop \
            -o "${REP_DIR}"/offload_overhead_with_decode_fix_offload_ratio_"${offload_ratio}"_input_len_"${input_len}"_num_locals_"${num_locals}".nsys-rep \
            -f true \
            python -m benchmarks.offload_benchmarks.benchmark_offload_metrics_with_offload_ratio \
            --input-len "${input_len}" \
            --num-locals "${num_locals}" \
            --offload-ratio "${offload_ratio}"
    done
}

# profile_offload_overhead_with_diff_offload_pct
profile_offload_overhead_with_diff_offload_ratio
make -j -f "${CUR_DIR}"/Makefile REP_DIR="${REP_DIR}"
