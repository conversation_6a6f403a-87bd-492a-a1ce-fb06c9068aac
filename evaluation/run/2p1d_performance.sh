#!/bin/bash
set -e

# shellcheck source=../../evaluation/utils/2p1d_utils.sh
source evaluation/utils/2p1d_utils.sh

function evaluate_2p1d_disagg_with_sharegpt() {
    NUM_REQS=300
    DATASET_NAME=sharegpt
    local req_rates
    req_rates=$(seq 1 10)
    for req_rate in "${req_rates[@]}"; do
        REQ_RATE=${req_rate} run_disagg_profile 1
        REQ_RATE=${req_rate} OFFLOAD_RATIO=0.7 run_offload_profile 1
    done
}

evaluate_2p1d_disagg_with_sharegpt
