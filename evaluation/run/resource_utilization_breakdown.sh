#!/bin/bash

set -e

CUR_DIR=$(dirname "$0")
RESULT_ROOT_DIR=${CUR_DIR}/../results
EVALUATE_NAME=$(basename "$0" .sh)
RESULT_DIR=${RESULT_ROOT_DIR}/${EVALUATE_NAME}
MODEL=${MODEL:-"meta-llama/Llama-2-7b-hf"}

INPUT_LEN=1024
OUTPUT_LEN=8
NUM_LOCALS=55

function profile_disagg_resource_utilization_breakdown() {
    local time
    time=$(date +"%Y-%m-%d_%H-%M-%S")

    local output_path="${RESULT_DIR}/disagg_prefill_num_locals_${NUM_LOCALS}_${time}"
    nsys profile \
        --gpu-metrics-devices=0 \
        --gpu-metrics-frequency=100000 \
        --capture-range=cudaProfilerApi \
        --capture-range-end=stop \
        --output "${output_path}" \
        python -m benchmarks.disagg_benchmarks.bench_disagg_metrics \
        --input-len ${INPUT_LEN} \
        --output-len ${OUTPUT_LEN} \
        --decode-batch ${NUM_LOCALS} \
        --model "${MODEL}"

    local output_path="${RESULT_DIR}/disagg_decode_num_locals_${NUM_LOCALS}_${time}"
    nsys profile \
        --gpu-metrics-devices=1 \
        --gpu-metrics-frequency=100000 \
        --capture-range=cudaProfilerApi \
        --capture-range-end=stop \
        --output "${output_path}" \
        python -m benchmarks.disagg_benchmarks.bench_disagg_metrics \
        --input-len ${INPUT_LEN} \
        --output-len ${OUTPUT_LEN} \
        --decode-batch ${NUM_LOCALS} \
        --enforce-eager \
        --model "${MODEL}"
}

function profile_offload_resource_utilization_breakdown() {
    time=$(date +"%Y-%m-%d_%H-%M-%S")
    local offload_ratios=(0.2 0.4 0.6 0.8)
    for offload_ratio in "${offload_ratios[@]}"; do
        local output_path="${RESULT_DIR}/offload_prefill_ratio_${offload_ratio}_num_locals_${NUM_LOCALS}_${time}"
        nsys profile \
            --gpu-metrics-devices=0 \
            --gpu-metrics-frequency=200000 \
            --capture-range=cudaProfilerApi \
            --capture-range-end=stop \
            --output "${output_path}" \
            python -m benchmarks.offload_benchmarks.benchmark_offload_metrics_with_offload_ratio \
            --input-len ${INPUT_LEN} \
            --output-len ${OUTPUT_LEN} \
            --num-locals ${NUM_LOCALS} \
            --offload-ratio "${offload_ratio}"

        local output_path="${RESULT_DIR}/offload_decode_ratio_${offload_ratio}_num_locals_${NUM_LOCALS}_${time}"
        nsys profile \
            --gpu-metrics-devices=1 \
            --gpu-metrics-frequency=200000 \
            --capture-range=cudaProfilerApi \
            --capture-range-end=stop \
            --output "${output_path}" \
            python -m benchmarks.offload_benchmarks.benchmark_offload_metrics_with_offload_ratio \
            --input-len ${INPUT_LEN} \
            --output-len ${OUTPUT_LEN} \
            --num-locals ${NUM_LOCALS} \
            --enforce-eager \
            --offload-ratio "${offload_ratio}"
    done
}

mkdir -p "${RESULT_DIR}"
profile_disagg_resource_utilization_breakdown
profile_offload_resource_utilization_breakdown
make -j -f "${CUR_DIR}/Makefile" REP_DIR="${RESULT_DIR}"
