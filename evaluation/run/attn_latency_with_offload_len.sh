#!/bin/bash

set -e

CUR_DIR=$(dirname "$0")
RESULT_ROOT_DIR=${CUR_DIR}/../results
EVALUATE_NAME=$(basename "$0" .sh)
RESULT_DIR=${RESULT_ROOT_DIR}/${EVALUATE_NAME}
mkdir -p "${RESULT_DIR}"

num_locals=60
offload_ratio=0.7
num_offloads=$(echo "($num_locals * $offload_ratio) / 1" | bc)
local_len=1024
offload_lens=(
    512 1024 1536 2048 2560
)
for offload_len in "${offload_lens[@]}"; do
    nsys profile \
        --gpu-metrics-devices=all \
        --gpu-metrics-frequency=100000 \
        --capture-range=cudaProfilerApi \
        --capture-range-end=stop \
        --cuda-graph-trace=node \
        --output "${RESULT_DIR}/attn_latency_with_offload_len_${offload_len}_local_len_${local_len}_offload_ratio_${offload_ratio}" \
        python -m benchmarks.offload_benchmarks.benchmark_offload_decode_with_offload_len \
        --num-locals "$num_locals" \
        --num-offloads "$num_offloads" \
        --local-input-len "$local_len" \
        --offload-input-len "$offload_len" \
        --offload-ratio $offload_ratio
done
