#!/bin/bash
set -e

# shellcheck source=../../evaluation/utils/1p1d_utils.sh
source evaluation/utils/1p1d_utils.sh

function evaluate_with_sharegpt_and_llama_2_7b() {
    NUM_REQS=20
    DATASET_NAME=sharegpt
    local req_rates=(
        # 1.5 2 2.5 3 3.5 4 4.5
        2
    )
    for req_rate in "${req_rates[@]}"; do
        REQ_RATE=${req_rate} run_disagg_profile 1
        # NUM_ATTENTION_BLOCKS=512 REQ_RATE=${req_rate} OFFLOAD_RATIO=0.6 run_offload_profile 1
        # REQ_RATE=${req_rate} OFFLOAD_RATIO=0.7 run_offload_profile 1
        # REQ_RATE=${req_rate} OFFLOAD_RATIO=0.8 run_offload_profile 1
    done
}

function evaluate_with_openthoughts_and_llama_2_7b() {
    NUM_REQS=70
    DATASET_NAME=open-thoughts
    local req_rates=(
        2 2.5 3 3.5 4 4.5 5
    )
    for req_rate in "${req_rates[@]}"; do
        REQ_RATE=${req_rate} run_disagg_profile 3
        NUM_ATTENTION_BLOCKS=6300 REQ_RATE=${req_rate} OFFLOAD_RATIO=0.6 run_offload_profile 3
        NUM_ATTENTION_BLOCKS=6300 REQ_RATE=${req_rate} OFFLOAD_RATIO=0.7 run_offload_profile 3
        NUM_ATTENTION_BLOCKS=6300 REQ_RATE=${req_rate} OFFLOAD_RATIO=0.8 run_offload_profile 3
    done
}

function evaluate_with_sharegpt_and_llama_2_13b() {
    NUM_REQS=70
    DATASET_NAME=sharegpt
    MODEL=meta-llama/Llama-2-13b-hf
    local req_rates=(
        1 1.5 2 2.5 3 3.5 4
    )
    for req_rate in "${req_rates[@]}"; do
        REQ_RATE=${req_rate} run_disagg_profile 1
        NUM_ATTENTION_BLOCKS=2048 REQ_RATE=${req_rate} OFFLOAD_RATIO=0.6 run_offload_profile 1
        NUM_ATTENTION_BLOCKS=2048 REQ_RATE=${req_rate} OFFLOAD_RATIO=0.7 run_offload_profile 1
        NUM_ATTENTION_BLOCKS=2048 REQ_RATE=${req_rate} OFFLOAD_RATIO=0.8 run_offload_profile 1
    done
}

function evaluate_with_openthoughts_and_llama_2_13b() {
    NUM_REQS=30
    DATASET_NAME=open-thoughts
    MODEL=meta-llama/Llama-2-13b-hf
    local req_rates=(
        1 1.5 2 2.5 3 3.5 4
    )
    for req_rate in "${req_rates[@]}"; do
        REQ_RATE=${req_rate} run_disagg_profile 1
        NUM_ATTENTION_BLOCKS=2846 REQ_RATE=${req_rate} OFFLOAD_RATIO=0.6 run_offload_profile 1
        NUM_ATTENTION_BLOCKS=2846 REQ_RATE=${req_rate} OFFLOAD_RATIO=0.7 run_offload_profile 1
        NUM_ATTENTION_BLOCKS=2846 REQ_RATE=${req_rate} OFFLOAD_RATIO=0.8 run_offload_profile 1
    done
}

function evaluate_with_openthoughts_and_llama_3_1_8b() {
    NUM_REQS=80
    DATASET_NAME=open-thoughts
    MODEL=meta-llama/Llama-3.1-8B
    # prevent cuda OOM
    MAX_MODEL_LEN=32768
    local req_rates=(
        # 1 1.5 2 2.5 3 3.5 4
        2
    )
    for req_rate in "${req_rates[@]}"; do
        REQ_RATE=${req_rate} run_disagg_profile 1
        NUM_PREFILL_BLOCKS=2048 NUM_ATTENTION_BLOCKS=15000 REQ_RATE=${req_rate} run_offload_profile 1
    done
}

# evaluate_with_sharegpt_and_llama_2_7b
# evaluate_with_openthoughts_and_llama_2_7b
# evaluate_with_sharegpt_and_llama_2_13b
# evaluate_with_openthoughts_and_llama_2_13b
evaluate_with_openthoughts_and_llama_3_1_8b
