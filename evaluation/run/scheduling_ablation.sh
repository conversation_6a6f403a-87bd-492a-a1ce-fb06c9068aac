#!/bin/bash

set -e

# shellcheck source=../../evaluation/utils/1p1d_utils.sh
source evaluation/utils/1p1d_utils.sh

CUR_DIR=$(dirname "$0")
RESULT_ROOT_DIR=${CUR_DIR}/../results
EVALUATE_NAME=$(basename "$0" .sh)
RESULT_DIR=${RESULT_ROOT_DIR}/${EVALUATE_NAME}

function profile_offload_by_native_schedule() {
    NUM_REQS=250
    DATASET_NAME=sharegpt
    local req_rates=(2 3 4)
    for req_rate in "${req_rates[@]}"; do
        RUN_TYPE=offload/pct_sche \
            OFFLOAD_BACKEND=percentage \
            OFFLOAD_RATIO=0.7 \
            REQ_RATE=${req_rate} \
            NUM_ATTENTION_BLOCKS=5120 \
            run_offload_profile 1
    done
}

mkdir -p "${RESULT_DIR}"
profile_offload_by_native_schedule
