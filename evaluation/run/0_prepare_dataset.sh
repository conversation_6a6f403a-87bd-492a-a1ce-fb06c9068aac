#!/bin/bash
set -e

DATASETS_DIR=${DATASETS_DIR:=evaluation/datasets}

python -m evaluation.run.python.prepare_dataset \
    --dataset sharegpt \
    --dataset-path "$DATASETS_DIR"/raw/ShareGPT_V3_unfiltered_cleaned_split.json \
    --output-path "$DATASETS_DIR"/sharegpt.ds

python -m evaluation.run.python.prepare_dataset \
    --dataset open-thoughts \
    --output-path "$DATASETS_DIR"/open-thoughts.ds

python -m evaluation.run.python.prepare_dataset \
    --dataset mooncake \
    --dataset-path "$DATASETS_DIR/raw/conversation_trace.jsonl" \
    --output-path "$DATASETS_DIR"/mooncake.ds

python -m evaluation.run.python.prepare_dataset \
    --dataset random \
    --random-input-len 12035 \
    --random-output-len 343 \
    --random-range-ratio 0.8 \
    --output-path "$DATASETS_DIR"/random_p12035_d343.ds

python -m evaluation.run.python.prepare_dataset \
    --dataset random \
    --random-input-len 833 \
    --random-output-len 9968 \
    --random-range-ratio 0.8 \
    --output-path "$DATASETS_DIR"/random_p833_d9968.ds

function hist_dataset_distribution() {
    local datasets=(
        sharegpt
        open-thoughts
        mooncake
        random_p12035_d343
        random_p833_d9968
    )
    for dataset in "${datasets[@]}"; do
        python -m evaluation.plots.prepare_datasets.dataset_histogram "${DATASETS_DIR}/${dataset}".ds \
            --output-dir evaluation/plots/prepare_datasets/"${dataset}"
    done
}

hist_dataset_distribution
