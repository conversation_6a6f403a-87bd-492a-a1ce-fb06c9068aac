#!/bin/bash
set -e

CUR_DIR=$(dirname "$0")
RESULT_ROOT_DIR=${CUR_DIR}/../results
EVALUATE_NAME=$(basename "$0" .sh)
REP_DIR=${RESULT_ROOT_DIR}/${EVALUATE_NAME}

function profile_attention_bandwidth_with_mps() {
    if [[ ! -d "${REP_DIR}" ]]; then
        mkdir -p "${REP_DIR}"
    fi

    local seqlens=(512 1024 2048)
    local batches=(16 32 64)
    local seqlen batch pct
    for seqlen in "${seqlens[@]}"; do
    for batch in "${batches[@]}"; do
    for pct in $(seq 10 10 100); do
        CUDA_MPS_ACTIVE_THREAD_PERCENTAGE=${pct} \
            nsys profile \
            --gpu-metrics-devices=all \
            --gpu-metrics-frequency=100000 \
            --capture-range=cudaProfilerApi \
            --capture-range-end=stop \
            -o "${REP_DIR}/attention_sm_${pct}_seqlen_${seqlen}_batch_${batch}.nsys-rep" \
            -f true \
            python -m benchmarks.attention_kernels.bench_attention \
            --avg-seq-len "${seqlen}" \
            --batch-size "${batch}"
    done
    done
    done
}

profile_attention_bandwidth_with_mps
make -j -f "${CUR_DIR}/Makefile" REP_DIR="${REP_DIR}"
