#!/bin/bash

set -e

CUR_DIR=$(dirname "$0")
RESULT_DIR="${CUR_DIR}/../results/offload_overhead_ablation"
mkdir -p "${RESULT_DIR}"

abla_tecs=(
    0 1 2 3
)
num_locals=(
    10 30 50
)
offload_ratio=0.6
for num_local in "${num_locals[@]}"; do
    for abla_tec in "${abla_tecs[@]}"; do
        num_offloads=$(echo "($num_local * $offload_ratio) / 1" | bc)
        python -m benchmarks.offload_benchmarks.benchmark_offload_decode \
            --num-locals "$num_local" \
            --num-offloads "$num_offloads" \
            --abla-enable-tec "$abla_tec" \
            --dataset-path evaluation/datasets/sharegpt.ds \
        | tee "${RESULT_DIR}/num_locals_$num_local-abla_tec_${abla_tec}.out"
    done
done
