#!/bin/bash

set -e

# shellcheck source=../../evaluation/utils/1p1d_utils.sh
source evaluation/utils/1p1d_utils.sh

CUR_DIR=$(dirname "$0")
RESULT_ROOT_DIR=${CUR_DIR}/../results
EVALUATE_NAME=$(basename "$0" .sh)
RESULT_DIR=${RESULT_ROOT_DIR}/${EVALUATE_NAME}

function profile_hbm_usage_for_disagg() {
    NUM_REQS=400
    DATASET_NAME=sharegpt
    local req_rate=4.5
    REQ_RATE=${req_rate} run_disagg_profile 1
    cp ./prefill.out "${RESULT_DIR}/disagg_prefill.out"
    cp ./decode.out "${RESULT_DIR}/disagg_decode.out"
}

function profile_hbm_usage_for_offload() {
    NUM_REQS=400
    DATASET_NAME=sharegpt
    local req_rate=4.5
    OFFLOAD_RATIO=0.7 REQ_RATE=${req_rate} run_offload_profile 1
    cp ./prefill.out "${RESULT_DIR}/offload_prefill.out"
    cp ./decode.out "${RESULT_DIR}/offload_decode.out"
    cp ./offload.out "${RESULT_DIR}/offload_offload.out"
}

mkdir -p "${RESULT_DIR}"
profile_hbm_usage_for_disagg
profile_hbm_usage_for_offload
