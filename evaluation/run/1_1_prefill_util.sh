#!/bin/bash

set -e

CUR_DIR=$(dirname "$0")
RESULT_ROOT_DIR=${CUR_DIR}/../results
EVALUATE_NAME=$(basename "$0" .sh)
RESULT_DIR=${RESULT_ROOT_DIR}/${EVALUATE_NAME}

# key args
# PROMPT_LENS=(1024 2028 ... 8192)
readarray -t PROMPT_LENS < <(seq 1024 1024 8192)
OUTPUT_LEN=8
DECODE_BATCH=1
MODEL=${MODEL:="meta-llama/Llama-2-7b-hf"}

function do_profiling() {
    echo "start do_profiling..."
    local arg_input_len=$1
    local arg_profile_out_path=$2
    nsys profile \
        --gpu-metrics-devices=0 \
        --gpu-metrics-frequency=100000 \
        --capture-range=cudaProfilerApi \
        --capture-range-end=stop \
        --output ${arg_profile_out_path} \
        python -m benchmarks.disagg_benchmarks.bench_disagg_metrics \
        --input-len ${arg_input_len} \
        --output-len ${OUTPUT_LEN} \
        --decode-batch ${DECODE_BATCH} \
        --model ${MODEL} \
        --enforce-eager
    sleep 5
}

mkdir -p ${RESULT_ROOT_DIR}
mkdir -p ${RESULT_DIR}

for len in "${PROMPT_LENS[@]}"; do
    time_str=$(date +"%Y-%m-%d_%H-%M-%S")
    report_path="${RESULT_DIR}/report_${EVALUATE_NAME}_${len}_${time_str}"
    do_profiling ${len} ${report_path}
done

make -j -f evaluation/run/Makefile REP_DIR="${RESULT_DIR}"
