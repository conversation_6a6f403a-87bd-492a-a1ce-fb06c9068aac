#!/bin/bash
set -e

CUR_DIR=$(dirname "$0")
RESULT_ROOT_DIR=${CUR_DIR}/../results
EVALUATE_NAME=$(basename "$0" .sh)
RESULT_DIR=${RESULT_ROOT_DIR}/${EVALUATE_NAME}
mkdir -p "${RESULT_DIR}"

batches=(4 8 16 24 32 40)
ops=(
    "scatter"
    "send_recv"
    "broadcast"
)

for batch in "${batches[@]}"; do
for op in "${ops[@]}"; do
    CUDA_VISIBLE_DEVICES=$RANK \
        nsys profile \
        --capture-range=cudaProfilerApi \
        --capture-range-end=stop \
        -o "${RESULT_DIR}/${batch}_${op}_${RANK}" \
        python \
        -m benchmarks.offload_benchmarks.transfer_latency \
        --rank "$RANK" \
        --batch "$batch" \
        --op "$op"
done
done
