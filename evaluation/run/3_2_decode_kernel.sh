#!/bin/bash

set -e

CUR_DIR=$(dirname "$0")
RESULT_ROOT_DIR=${CUR_DIR}/../results
EVALUATE_NAME=$(basename "$0" .sh)
RESULT_DIR=${RESULT_ROOT_DIR}/${EVALUATE_NAME}

# key args
PROMPT_LEN=1024
OUTPUT_LEN=8
# DECODE_BATCH=(8 16 ... 128)
readarray -t DECODE_BATCH < <(seq 8 8 80)
MODEL="meta-llama/Llama-2-7b-hf"

mkdir -p ${RESULT_ROOT_DIR}
mkdir -p ${RESULT_DIR}

do_profiling() {
    echo "start do_profiling..."
    local arg_decode_batch=$1
    local arg_profile_out_path=$2
    nsys profile \
        --gpu-metrics-devices=1 \
        --gpu-metrics-frequency=100000 \
        --capture-range=cudaProfilerApi \
        --capture-range-end=stop \
        --output ${arg_profile_out_path} \
        --force-overwrite true \
        python -m benchmarks.disagg_benchmarks.bench_disagg_metrics \
        --input-len ${PROMPT_LEN} \
        --output-len ${OUTPUT_LEN} \
        --decode-batch ${arg_decode_batch} \
        --model ${MODEL} \
        --enforce-eager
    sleep 5
}

for bs in "${DECODE_BATCH[@]}"; do
    time_str=$(date +"%Y-%m-%d_%H-%M-%S")
    report_path="${RESULT_DIR}/report_${EVALUATE_NAME}_${bs}_eager_${time_str}"
    do_profiling ${bs} ${report_path}
done

make -j -f evaluation/run/Makefile REP_DIR="${RESULT_DIR}"
