#!/bin/bash

set -e

CUR_DIR=$(dirname "$0")
RESULT_ROOT_DIR=${CUR_DIR}/../results
EVALUATE_NAME=$(basename "$0" .sh)
RESULT_DIR=${RESULT_ROOT_DIR}/${EVALUATE_NAME}


function profile_disagg_resource_utilization() {
    local time
    time=$(date +"%Y-%m-%d_%H-%M-%S")
    local model_name
    model_name=$(basename "${MODEL}")

    local output_path="${RESULT_DIR}/disagg_prefill_${model_name}_num_locals_${NUM_LOCALS}_${time}"
    nsys profile \
        --gpu-metrics-devices=0 \
        --gpu-metrics-frequency=100000 \
        --capture-range=cudaProfilerApi \
        --capture-range-end=stop \
        --output "${output_path}" \
        python -m benchmarks.disagg_benchmarks.bench_disagg_metrics \
        --input-len ${INPUT_LEN} \
        --output-len ${OUTPUT_LEN} \
        --decode-batch ${NUM_LOCALS} \
        --model "${MODEL}"

    local output_path="${RESULT_DIR}/disagg_decode_${model_name}_num_locals_${NUM_LOCALS}_${time}"
    nsys profile \
        --gpu-metrics-devices=1 \
        --gpu-metrics-frequency=100000 \
        --capture-range=cudaProfilerApi \
        --capture-range-end=stop \
        --output "${output_path}" \
        python -m benchmarks.disagg_benchmarks.bench_disagg_metrics \
        --input-len ${INPUT_LEN} \
        --output-len ${OUTPUT_LEN} \
        --decode-batch ${NUM_LOCALS} \
        --model "${MODEL}"
}

function profile_offload_resource_utilization() {
    local time
    time=$(date +"%Y-%m-%d_%H-%M-%S")
    local model_name
    model_name=$(basename "${MODEL}")

    local offload_ratios=(0.2 0.4 0.6 0.8)
    for ratio in "${offload_ratios[@]}"; do
        local output_path="${RESULT_DIR}/offload_prefill_${ratio}_${model_name}_num_locals_${NUM_LOCALS}_${time}"
        nsys profile \
            --gpu-metrics-devices=0 \
            --gpu-metrics-frequency=200000 \
            --capture-range=cudaProfilerApi \
            --capture-range-end=stop \
            --output "${output_path}" \
            python -m benchmarks.offload_benchmarks.benchmark_offload_metrics_with_offload_ratio \
            --model "${MODEL}" \
            --input-len ${INPUT_LEN} \
            --output-len ${OUTPUT_LEN} \
            --num-locals ${NUM_LOCALS} \
            --offload-ratio "${ratio}"

        local output_path="${RESULT_DIR}/offload_decode_${ratio}_${model_name}_num_locals_${NUM_LOCALS}_${time}"
        nsys profile \
            --gpu-metrics-devices=1 \
            --gpu-metrics-frequency=200000 \
            --capture-range=cudaProfilerApi \
            --capture-range-end=stop \
            --output "${output_path}" \
            python -m benchmarks.offload_benchmarks.benchmark_offload_metrics_with_offload_ratio \
            --model "${MODEL}" \
            --input-len ${INPUT_LEN} \
            --output-len ${OUTPUT_LEN} \
            --num-locals ${NUM_LOCALS} \
            --offload-ratio "${ratio}"
    done
}

INPUT_LEN=1024
OUTPUT_LEN=8
NUM_LOCALS=55

mkdir -p "${RESULT_DIR}"
MODEL="meta-llama/Llama-2-7b-hf" profile_disagg_resource_utilization
MODEL="meta-llama/Llama-2-7b-hf" profile_offload_resource_utilization
INPUT_LEN=512
NUM_LOCALS=50
MODEL="meta-llama/Llama-2-13b-hf" profile_disagg_resource_utilization
MODEL="meta-llama/Llama-2-13b-hf" profile_offload_resource_utilization
make -j -f "${CUR_DIR}/Makefile" REP_DIR="${RESULT_DIR}"
