# Evaluation

## Quick Start

### Install Dependencies

```
git checkout kuntai-disagg-refactor
pip install -e .
git checkout offload/evaluation
python adrenaline/setup.py install
```

### Prepare Dataset

prepare sharegpt:

```bash
mkdir -p evaluation/datasets/raw
curl -L https://huggingface.co/datasets/anon8231489123/ShareGPT_Vicuna_unfiltered/resolve/main/ShareGPT_V3_unfiltered_cleaned_split.json -o evaluation/datasets/raw/ShareGPT_V3_unfiltered_cleaned_split.json
bash evaluation/run/0_prepare_dataset.sh
```

prepare mooncake:

```bash
mkdir -p evaluation/datasets/raw
curl -L https://raw.githubusercontent.com/kvcache-ai/Mooncake/refs/heads/main/FAST25-release/traces/conversation_trace.jsonl -o evaluation/datasets/raw/conversation_trace.jsonl
```
