#!/bin/bash
# Usage examples for the modified CUDA device allocation scripts

echo "=== CUDA Device Allocation Usage Examples ==="
echo
echo "The disagg_instances_start.sh script now supports custom CUDA device allocation."
echo "Here are various ways to use it:"
echo

echo "1. Default behavior (uses GPUs 0,1,2,3 automatically):"
echo "   ./disagg_instances_start.sh"
echo

echo "2. Specify available GPUs for automatic distribution:"
echo "   AVAILABLE_CUDA_DEVICES=\"0,2,4,6\" ./disagg_instances_start.sh"
echo

echo "3. Specify number of prefill instances with custom GPUs:"
echo "   PREFILL_INSTANCES=3 AVAILABLE_CUDA_DEVICES=\"1,3,5,7\" ./disagg_instances_start.sh"
echo

echo "4. Explicit device assignment for all instances:"
echo "   PREFILL_CUDA_DEVICES_EXPLICIT=\"0,2\" DECODE_CUDA_DEVICE_EXPLICIT=\"4\" ./disagg_instances_start.sh"
echo

echo "5. Mixed assignment (explicit prefill, automatic decode):"
echo "   PREFILL_CUDA_DEVICES_EXPLICIT=\"1,3\" AVAILABLE_CUDA_DEVICES=\"0,1,2,3,4,5\" ./disagg_instances_start.sh"
echo

echo "6. With other model parameters:"
echo "   MODEL=\"meta-llama/Llama-2-13b-hf\" AVAILABLE_CUDA_DEVICES=\"0,1,2,3\" ./disagg_instances_start.sh"
echo

echo "=== Environment Variables Summary ==="
echo
echo "AVAILABLE_CUDA_DEVICES       - Comma-separated list of GPU IDs for automatic distribution"
echo "PREFILL_CUDA_DEVICES_EXPLICIT - Comma-separated list of GPU IDs for prefill instances (explicit)"
echo "DECODE_CUDA_DEVICE_EXPLICIT   - Single GPU ID for decode instance (explicit)"
echo "PREFILL_INSTANCES             - Number of prefill instances (default: 1)"
echo
echo "Note: Explicit assignments take precedence over automatic distribution."
echo "If you specify explicit assignments, make sure the number of devices matches"
echo "the number of prefill instances."
