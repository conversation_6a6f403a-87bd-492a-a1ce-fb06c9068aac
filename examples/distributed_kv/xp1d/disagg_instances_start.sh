#!/bin/bash
# This file demonstrates the example usage of disaggregated prefilling
# We will launch (x+1) vllm instances (x for prefill and 1 for decode),
# and then transfer the KV cache between them.
#
# CUDA Device Allocation:
# Option 1 - Automatic distribution:
#   Set AVAILABLE_CUDA_DEVICES environment variable to specify which GPUs to use.
#   Example: AVAILABLE_CUDA_DEVICES="0,1,2,3" ./disagg_instances_start.sh
#   If not set, defaults to "0,1,2,3"
#   The script will automatically distribute devices between instances.
#
# Option 2 - Explicit assignment:
#   Set PREFILL_CUDA_DEVICES_EXPLICIT for prefill instances (comma-separated)
#   Set DECODE_CUDA_DEVICE_EXPLICIT for decode instance (single device)
#   Example: PREFILL_CUDA_DEVICES_EXPLICIT="0,1" DECODE_CUDA_DEVICE_EXPLICIT="2" ./disagg_instances_start.sh

# shellcheck source=../../../examples/distributed_utils/utils.sh
source examples/distributed_utils/utils.sh

# Function to parse and distribute CUDA devices
function distribute_cuda_devices() {
    local available_devices="${AVAILABLE_CUDA_DEVICES:-0,1,2,3,4,5,6,7}"
    local prefill_instances="${PREFILL_INSTANCES:-1}"

    # Check if explicit device assignments are provided
    if [[ -n "${PREFILL_CUDA_DEVICES_EXPLICIT}" ]]; then
        # Use explicit assignments for prefill instances
        IFS=',' read -ra PREFILL_CUDA_DEVICES <<< "$PREFILL_CUDA_DEVICES_EXPLICIT"
        echo "Using explicit prefill device assignments: $PREFILL_CUDA_DEVICES_EXPLICIT"
    else
        # Convert comma-separated string to array
        IFS=',' read -ra DEVICE_ARRAY <<< "$available_devices"
        local total_devices=${#DEVICE_ARRAY[@]}

        # Calculate devices needed (prefill instances + 1 decode instance)
        local total_instances=$((prefill_instances + 1))

        if [ "$total_devices" -lt "$total_instances" ]; then
            echo "Warning: Not enough CUDA devices ($total_devices) for all instances ($total_instances)"
            echo "Some instances will share devices"
        fi

        # Distribute devices to prefill instances
        PREFILL_CUDA_DEVICES=()
        for ((i=0; i<prefill_instances; i++)); do
            local device_idx=$((i % total_devices))
            PREFILL_CUDA_DEVICES[i]=${DEVICE_ARRAY[device_idx]}
        done
    fi

    # Handle decode device assignment
    if [[ -n "${DECODE_CUDA_DEVICE_EXPLICIT}" ]]; then
        DECODE_CUDA_DEVICE="$DECODE_CUDA_DEVICE_EXPLICIT"
        echo "Using explicit decode device assignment: $DECODE_CUDA_DEVICE_EXPLICIT"
    else
        # Convert comma-separated string to array for automatic assignment
        IFS=',' read -ra DEVICE_ARRAY <<< "$available_devices"
        local total_devices=${#DEVICE_ARRAY[@]}
        # Assign device to decode instance (use the next available device or wrap around)
        local decode_device_idx=$((prefill_instances % total_devices))
        DECODE_CUDA_DEVICE=${DEVICE_ARRAY[decode_device_idx]}
    fi

    # Export for use in sub-scripts
    export PREFILL_CUDA_DEVICES
    export DECODE_CUDA_DEVICE

    echo "CUDA Device Distribution:"
    echo "Available devices: $available_devices"
    echo "Prefill instances: $prefill_instances"
    for ((i=0; i<prefill_instances; i++)); do
        echo "  Prefill instance $i: GPU ${PREFILL_CUDA_DEVICES[i]}"
    done
    echo "  Decode instance: GPU $DECODE_CUDA_DEVICE"
}

function start_disagg_tmux_session() {
    PREFILL_INSTANCES=${PREFILL_INSTANCES:=1}
    tmux new-session -d -n proxy -s disagg bash
    for ((i=0; i<PREFILL_INSTANCES; i++)); do
        tmux new-window -n "p$i" -t disagg: bash
    done
    tmux new-window -n d0 -t disagg: bash
}

function kill_disagg_tmux_session() {
    tmux kill-session -t disagg
}

function start_disagg_instances() {
    # Distribute CUDA devices before starting instances
    distribute_cuda_devices

    for ((i=0; i<PREFILL_INSTANCES; i++)); do
        ASSIGNED_CUDA_DEVICE=${PREFILL_CUDA_DEVICES[i]} bash "examples/distributed_kv/xp1d/prefill_instance_start.sh" $i 2>&1 | tee "prefill_${i}.out" &
    done
    ASSIGNED_CUDA_DEVICE=$DECODE_CUDA_DEVICE bash examples/distributed_kv/xp1d/decode_instance_start.sh 2>&1 | tee decode.out &
}

function start_disagg_instances_in_tmux() {
    # Distribute CUDA devices before starting instances
    distribute_cuda_devices

    if [[ -n "${MODEL}" ]]; then
        for ((i=0; i<PREFILL_INSTANCES; i++)); do
            tmux send-key -t "disagg:p${i}" "MODEL=${MODEL} "
        done
        tmux send-key -t disagg:d0 "MODEL=${MODEL} "
    fi

    if [[ -n "${MAX_MODEL_LEN}" ]]; then
        for ((i=0; i<PREFILL_INSTANCES; i++)); do
            tmux send-key -t "disagg:p${i}" "MAX_MODEL_LEN=${MAX_MODEL_LEN} "
        done
        tmux send-key -t disagg:d0 "MAX_MODEL_LEN=${MAX_MODEL_LEN} "
    fi

    if [[ -n "${LOAD_FORMAT}" ]]; then
        for ((i=0; i<PREFILL_INSTANCES; i++)); do
            tmux send-key -t "disagg:p${i}" "LOAD_FORMAT=${LOAD_FORMAT} "
        done
        tmux send-key -t disagg:d0 "LOAD_FORMAT=${LOAD_FORMAT} "
    fi

    for ((i=0; i<PREFILL_INSTANCES; i++)); do
        tmux send-key -t "disagg:p${i}" "PREFILL_INSTANCES=${PREFILL_INSTANCES} "
    done
    tmux send-key -t disagg:d0 "PREFILL_INSTANCES=${PREFILL_INSTANCES} "

    for ((i=0; i<PREFILL_INSTANCES; i++)); do
        local port=$((8100 + i * 100))
        tmux send-key -t "disagg:p${i}" \
            "PORT=${port} ASSIGNED_CUDA_DEVICE=${PREFILL_CUDA_DEVICES[i]} bash examples/distributed_kv/xp1d/prefill_instance_start.sh $i 2>&1 | tee prefill_${i}.out" ENTER
    done
    local port=$((8100 + PREFILL_INSTANCES * 100))
    tmux send-key -t disagg:d0 \
        "PORT=${port} ASSIGNED_CUDA_DEVICE=$DECODE_CUDA_DEVICE bash examples/distributed_kv/xp1d/decode_instance_start.sh 2>&1 | tee decode.out" ENTER
}

function wait_disagg_instances_running() {
    local starttime
    starttime=$(date +%s)
    local passtime
    passtime=$(($(date +%s) - "$starttime"))

    local processes=()
    for ((i=0; i<PREFILL_INSTANCES; i++)); do
        processes+=("prefill_instance_start.sh $i")
    done
    processes+=("decode_instance_start")

    local flags=(false)
    for ((i=0; i<PREFILL_INSTANCES; i++)); do
        flags+=(false)
    done

    while [[ ${flags[*]} =~ false ]]; do
        for ident in "${!processes[@]}"; do
            if check_process_running "${processes[$ident]}"; then
                flags[ident]=true
            fi
        done
        passtime=$(($(date +%s) - "$starttime"))
        echo "Some processes are not running (${flags[*]}, $passtime s passed). Waiting..."
        sleep 1
    done
    echo "All disagg processes are running."
}

function wait_disagg_instances_ready() {
    local ports=()
    for ((i=0; i<PREFILL_INSTANCES; i++)); do
        ports+=($((8100 + i * 100)))
    done
    ports+=($((8100 + PREFILL_INSTANCES * 100)))

    local processes=()
    for ((i=0; i<PREFILL_INSTANCES; i++)); do
        processes+=("prefill_instance_start.sh $i")
    done
    processes+=("decode_instance_start")

    local starttime
    starttime=$(date +%s)
    local timeout=240
    local passtime
    passtime=$(($(date +%s) - "$starttime"))
    while (("$passtime" < "$timeout")); do
        sleep 10
        passtime=$(($(date +%s) - "$starttime"))
        local flag=true
        for port in "${ports[@]}"; do
            if ! check_port_serving "${port}"; then
                echo "Port ${port} is not serving."
                flag=false
            fi
        done
        if [[ "$flag" = true ]]; then
            echo "All disagg instances are serving."
            return 0
        fi
        for process in "${processes[@]}"; do
            if ! check_process_running "$process"; then
                echo "$process is not running."
                return 1
            fi
        done
        echo "Some instances are not serving (${passtime}s passed with timeout ${timeout}s). Waiting..."
    done
}

function wait_disagg_instances_fin() {
    local starttime
    starttime=$(date +%s)
    local timeout=120
    local passtime
    passtime=$(($(date +%s) - starttime))

    local processes=()
    for ((i=0; i<PREFILL_INSTANCES; i++)); do
        processes+=("prefill_instance_start.sh $i")
    done
    processes+=("decode_instance_start")

    while ((passtime < timeout)); do
        sleep 2
        passtime=$(($(date +%s) - starttime))
        local flag=true
        for ident in "${!processes[@]}"; do
            if check_process_running "${processes[$ident]}"; then
                flag=false
            fi
        done
        if [[ "$flag" = true ]]; then
            echo "All disagg instances are finished."
            return 0
        fi
    done
    return 1
}

function start_disagg_proxy() {
    bash examples/distributed_kv/xp1d/proxy_instance_start.sh | tee proxy.out &
}

function start_disagg_proxy_in_tmux() {
    if [[ -n "${MODEL}" ]]; then
        tmux send-key -t disagg:proxy "MODEL=${MODEL} "
    fi

    tmux send-key -t disagg:proxy "PREFILL_INSTANCES=${PREFILL_INSTANCES} "
    tmux send-key -t disagg:proxy \
        "bash examples/distributed_kv/xp1d/proxy_instance_start.sh | tee proxy.out" ENTER
}

function kill_disagg_proxy_server() {
    pkill -f "disagg_prefill_proxy_server"
}

function wait_disagg_proxy_ready() {
    local starttime
    starttime=$(date +%s)
    local timeout=30
    local passtime
    passtime=$(($(date +%s) - starttime))
    while (("$passtime" < "$timeout")); do
        if check_port_serving 8000; then
            return 0
        fi
        passtime=$(($(date +%s) - starttime))
    done
    return 1
}

function start_disagg_environemnt() {
    if [ -z "$HF_HUB_OFFLINE" ]; then
        echo "HF_HUB_OFFLINE is not set. Please set it globaly to 1."
        exit 1
    fi
    start_disagg_tmux_session

    start_disagg_instances_in_tmux
    wait_disagg_instances_running
    while ! wait_disagg_instances_ready; do
        echo "Some instances have failed to start. Restarting..."
        kill_openai_servers
        wait_disagg_instances_fin
        sleep 5
        start_disagg_instances_in_tmux
        wait_disagg_instances_running
    done

    start_disagg_proxy_in_tmux
    wait_disagg_proxy_ready
}

function stop_disagg_environment() {
    kill_openai_servers
    kill_disagg_proxy_server
    wait_disagg_instances_fin
    kill_disagg_tmux_session
}
