#!/bin/bash
# Test script to verify CUDA device allocation functionality
# This script tests the device distribution logic without actually starting vLLM instances

# Source the main script to access the distribute_cuda_devices function
source examples/distributed_kv/xp1d/disagg_instances_start.sh

echo "=== Testing CUDA Device Allocation ==="
echo

# Test 1: Default behavior
echo "Test 1: Default behavior (no environment variables set)"
unset AVAILABLE_CUDA_DEVICES PREFILL_CUDA_DEVICES_EXPLICIT DECODE_CUDA_DEVICE_EXPLICIT
PREFILL_INSTANCES=2
distribute_cuda_devices
echo

# Test 2: Custom available devices
echo "Test 2: Custom available devices"
AVAILABLE_CUDA_DEVICES="0,2,4,6"
PREFILL_INSTANCES=3
unset PREFILL_CUDA_DEVICES_EXPLICIT DECODE_CUDA_DEVICE_EXPLICIT
distribute_cuda_devices
echo

# Test 3: More instances than devices
echo "Test 3: More instances than available devices"
AVAILABLE_CUDA_DEVICES="0,1"
PREFILL_INSTANCES=4
unset PREFILL_CUDA_DEVICES_EXPLICIT DECODE_CUDA_DEVICE_EXPLICIT
distribute_cuda_devices
echo

# Test 4: Explicit device assignment
echo "Test 4: Explicit device assignment"
PREFILL_CUDA_DEVICES_EXPLICIT="1,3,5"
DECODE_CUDA_DEVICE_EXPLICIT="7"
PREFILL_INSTANCES=3
distribute_cuda_devices
echo

# Test 5: Mixed explicit and automatic
echo "Test 5: Mixed explicit (prefill only)"
PREFILL_CUDA_DEVICES_EXPLICIT="2,4"
unset DECODE_CUDA_DEVICE_EXPLICIT
AVAILABLE_CUDA_DEVICES="0,1,2,3,4,5"
PREFILL_INSTANCES=2
distribute_cuda_devices
echo

echo "=== All tests completed ==="
