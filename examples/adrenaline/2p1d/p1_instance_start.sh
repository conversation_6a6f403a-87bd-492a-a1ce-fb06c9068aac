#!/bin/bash
# This file demonstrates the example usage of disaggregated prefilling
# We will launch 2 vllm instances (1 for prefill and 1 for decode),
# and then transfer the KV cache between them.

VLLM_HOST_IP=${VLLM_HOST_IP:=$(hostname -I | awk '{print $1}')}
export VLLM_HOST_IP
VLLM_PORT=${VLLM_PORT:=12345}
export VLLM_PORT

MODEL=${MODEL:=meta-llama/Llama-2-7b-hf}
LOAD_FORMAT=${LOAD_FORMAT:=auto}
NUM_BLOCKS=${NUM_BLOCKS:=512}
MAX_MODEL_LEN=${MAX_MODEL_LEN:=4096}

if [ "$MAX_MODEL_LEN" -gt 8192 ]; then
    MAX_SEQ_LEN_TO_CAPTURE=$MAX_MODEL_LEN
else
    MAX_SEQ_LEN_TO_CAPTURE=""
fi

# prefilling instance, which is the KV producer
VLLM_WORKER_MULTIPROC_METHOD=spawn \
VLLM_ATTENTION_BACKEND=ADRENALINE_FLASHINFER \
VLLM_LOGGING_LEVEL=DEBUG \
VLLM_ENABLE_KV_OFFLOAD=1 \
CUDA_MPS_ACTIVE_THREAD_PERCENTAGE=70 \
VLLM_DISTRIBUTED_KV_ROLE=producer \
VLLM_PREFILL_INSTANCES=2 \
VLLM_RANK_IN_INSTANCES=1 \
CUDA_VISIBLE_DEVICES=1 \
python3 \
    -m vllm.entrypoints.openai.api_server \
    --disable-log-stats \
    --model "${MODEL}" \
    --load-format "${LOAD_FORMAT}" \
    --max-model-len "${MAX_MODEL_LEN}" \
    --max-seq-len-to-capture ${MAX_SEQ_LEN_TO_CAPTURE} \
    --enforce-eager \
    --port 8200 \
    --num-gpu-blocks-override "${NUM_BLOCKS}"
