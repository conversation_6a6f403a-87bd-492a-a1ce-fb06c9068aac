#!/bin/bash
set -e

# shellcheck source=../../../examples/distributed_utils/utils.sh
source examples/distributed_utils/utils.sh

function start_adrenaline_instances() {
    OFFLOAD_RATIO=${OFFLOAD_RATIO} \
        bash examples/adrenaline/2p1d/p0_instance_start.sh |
        tee p0.out &

    OFFLOAD_RATIO=${OFFLOAD_RATIO} \
        bash examples/adrenaline/2p1d/p1_instance_start.sh |
        tee p1.out &

    OFFLOAD_RATIO=${OFFLOAD_RATIO} \
        bash examples/adrenaline/2p1d/o0_instance_start.sh |
        tee o0.out &

    OFFLOAD_RATIO=${OFFLOAD_RATIO} \
        bash examples/adrenaline/2p1d/o1_instance_start.sh |
        tee o1.out &

    OFFLOAD_RATIO=${OFFLOAD_RATIO} \
        bash examples/adrenaline/2p1d/d0_instance_start.sh |
        tee d0.out &
}

function start_adrenaline_tmux_session() {
    tmux new-session -d -n proxy -s adrenaline bash
    tmux new-window -n p0 -t adrenaline: bash
    tmux new-window -n p1 -t adrenaline: bash
    tmux new-window -n o0 -t adrenaline: bash
    tmux new-window -n o1 -t adrenaline: bash
    tmux new-window -n d0 -t adrenaline: bash
}

function kill_adrenaline_tmux_session() {
    tmux kill-session -t adrenaline
}

function start_adrenaline_instances_in_tmux() {
    if [[ -n "${MODEL}" ]]; then
        tmux send-key -t adrenaline:p0 "MODEL=${MODEL} "
        tmux send-key -t adrenaline:p1 "MODEL=${MODEL} "
        tmux send-key -t adrenaline:o0 "MODEL=${MODEL} "
        tmux send-key -t adrenaline:o1 "MODEL=${MODEL} "
        tmux send-key -t adrenaline:d0 "MODEL=${MODEL} "
    fi

    if [[ -n "${MAX_MODEL_LEN}" ]]; then
        tmux send-key -t adrenaline:p0 "MAX_MODEL_LEN=${MAX_MODEL_LEN} "
        tmux send-key -t adrenaline:p1 "MAX_MODEL_LEN=${MAX_MODEL_LEN} "
        tmux send-key -t adrenaline:o0 "MAX_MODEL_LEN=${MAX_MODEL_LEN} "
        tmux send-key -t adrenaline:o1 "MAX_MODEL_LEN=${MAX_MODEL_LEN} "
        tmux send-key -t adrenaline:d0 "MAX_MODEL_LEN=${MAX_MODEL_LEN} "
    fi

    if [[ -n "${LOAD_FORMAT}" ]]; then
        tmux send-key -t adrenaline:p0 "LOAD_FORMAT=${LOAD_FORMAT} "
        tmux send-key -t adrenaline:p1 "LOAD_FORMAT=${LOAD_FORMAT} "
        tmux send-key -t adrenaline:o0 "LOAD_FORMAT=${LOAD_FORMAT} "
        tmux send-key -t adrenaline:o1 "LOAD_FORMAT=${LOAD_FORMAT} "
        tmux send-key -t adrenaline:d0 "LOAD_FORMAT=${LOAD_FORMAT} "
    fi

    if [[ -n "${OFFLOAD_RATIO}" ]]; then
        tmux send-key -t adrenaline:p0 "OFFLOAD_RATIO=${OFFLOAD_RATIO} "
        tmux send-key -t adrenaline:p1 "OFFLOAD_RATIO=${OFFLOAD_RATIO} "
        tmux send-key -t adrenaline:o0 "OFFLOAD_RATIO=${OFFLOAD_RATIO} "
        tmux send-key -t adrenaline:o1 "OFFLOAD_RATIO=${OFFLOAD_RATIO} "
        tmux send-key -t adrenaline:d0 "OFFLOAD_RATIO=${OFFLOAD_RATIO} "
    fi

    if [[ -n "${NUM_PREFILL_BLOCKS}" ]]; then
        tmux send-key -t adrenaline:p0 "NUM_BLOCKS=${NUM_PREFILL_BLOCKS} "
        tmux send-key -t adrenaline:p1 "NUM_BLOCKS=${NUM_PREFILL_BLOCKS} "
    fi
    tmux send-key -t adrenaline:p0 \
        "bash examples/adrenaline/2p1d/p0_instance_start.sh 2>&1 \
        | tee p0.out" ENTER

    tmux send-key -t adrenaline:p1 \
        "bash examples/adrenaline/2p1d/p1_instance_start.sh 2>&1 \
        | tee p1.out" ENTER

    tmux send-key -t adrenaline:d0 \
        "bash examples/adrenaline/2p1d/d0_instance_start.sh 2>&1 \
        | tee d0.out" ENTER

    if [[ -n "${NUM_ATTENTION_BLOCKS}" ]]; then
        tmux send-key -t adrenaline:o0 "NUM_BLOCKS=${NUM_ATTENTION_BLOCKS} "
        tmux send-key -t adrenaline:o1 "NUM_BLOCKS=${NUM_ATTENTION_BLOCKS} "
    fi
    tmux send-key -t adrenaline:o0 \
        "bash examples/adrenaline/2p1d/o0_instance_start.sh 2>&1 \
        | tee o0.out" ENTER

    tmux send-key -t adrenaline:o1 \
        "bash examples/adrenaline/2p1d/o1_instance_start.sh 2>&1 \
        | tee o1.out" ENTER
}

function wait_adrenaline_instances_running() {
    local starttime
    starttime=$(date +%s)
    local passtime

    local processes=(
        p0_instance_start
        p1_instance_start
        d0_instance_start
        o0_instance_start
        o1_instance_start
    )
    local flags=(false false false false false)
    while [[ ${flags[*]} =~ false ]]; do
        passtime=$(($(date +%s) - "$starttime"))
        for ident in "${!processes[@]}"; do
            if check_process_running "${processes[$ident]}"; then
                flags[ident]=true
            fi
        done
        echo "Some processes are not running (${flags[*]}, ${passtime}s passed). Waiting..."
        sleep 1
    done
    echo "All adrenaline processes are running."
}

function wait_adrenaline_instances_ready() {
    local starttime
    starttime=$(date +%s)
    local timeout=300
    local ports=(
        8100
        8200
        8300
        8400
        8500
    )
    local processes=(
        p0_instance_start
        p1_instance_start
        d0_instance_start
        o0_instance_start
        o1_instance_start
    )
    local passtime
    passtime=$(($(date +%s) - "$starttime"))
    while (("$passtime" < "$timeout")); do
        sleep 10
        passtime=$(($(date +%s) - "$starttime"))
        local flag=true
        for port in "${ports[@]}"; do
            if ! check_port_serving "${port}"; then
                echo "Port ${port} is not serving."
                flag=false
            fi
        done
        if [[ "$flag" = true ]]; then
            echo "All adrenaline instances are serving."
            return 0
        fi
        for ident in "${processes[@]}"; do
            if ! check_process_running "$ident"; then
                echo "$ident is not running."
                return 1
            fi
        done
        echo "Some instances are not serving (${passtime}s passed with timeout ${timeout}s). Waiting..."
    done
}

function wait_adrenaline_instances_fin() {
    local starttime
    starttime=$(date +%s)
    local timeout=120
    local passtime
    passtime=$(($(date +%s) - starttime))
    local processes=(
        p0_instance_start
        p1_instance_start
        d0_instance_start
        o0_instance_start
        o1_instance_start
    )
    while ((passtime < timeout)); do
        sleep 2
        passtime=$(($(date +%s) - starttime))
        local flag=true
        for ident in "${!processes[@]}"; do
            if check_process_running "${processes[$ident]}"; then
                flag=false
            fi
        done
        if [[ "$flag" = true ]]; then
            echo "All adrenaline instances are finished."
            return 0
        fi
    done
    return 1
}

function start_adrenaline_proxy() {
    if [ ! -f d0.out ]; then
        echo "d0.out is not found. Please run the decode instance first."
        exit 1
    fi
    local num_decode_blocks
    num_decode_blocks=$(grep "GPU blocks" d0.out | sed "s/.*GPU blocks: //g" | sed "s/,.*//g")
    local num_attention_blocks=${NUM_ATTENTION_BLOCKS}
    OFFLOAD_RATIO=${OFFLOAD_RATIO} \
        NUM_DECODE_BLOCKS=${num_decode_blocks} \
        NUM_ATTENTION_BLOCKS=${num_attention_blocks} \
        bash examples/adrenaline/2p1d/proxy_instance_start.sh |
        tee proxy.out &
}

function start_adrenaline_proxy_in_tmux() {
    if [ ! -f d0.out ]; then
        echo "d0.out is not found. Please run the decode instance first."
        exit 1
    fi
    local num_decode_blocks
    num_decode_blocks=$(grep "GPU blocks" d0.out | sed "s/.*GPU blocks: //g" | sed "s/,.*//g")
    local num_attention_blocks
    num_attention_blocks=$(echo "(${num_decode_blocks} * ${OFFLOAD_RATIO}) / 1" | bc)
    if [[ -n "${MODEL}" ]]; then
        tmux send-key -t adrenaline:proxy "MODEL=${MODEL} "
    fi
    if [[ -n "${OFFLOAD_BACKEND}" ]]; then
        tmux send-key -t adrenaline:proxy \
            "OFFLOAD_BACKEND=${OFFLOAD_BACKEND} "
    fi
    tmux send-key -t adrenaline:proxy \
        "OFFLOAD_RATIO=${OFFLOAD_RATIO} \
        NUM_DECODE_BLOCKS=${num_decode_blocks} \
        NUM_ATTENTION_BLOCKS=${num_attention_blocks} \
        bash examples/adrenaline/2p1d/proxy_instance_start.sh \
        | tee proxy.out" ENTER
}

function kill_adrenaline_proxy_server() {
    pkill -f "adrenaline_proxy_server"
}

function wait_adrenaline_proxy_ready() {
    local starttime
    starttime=$(date +%s)
    local timeout=30
    local passtime
    passtime=$(($(date +%s) - "$starttime"))
    while (("$passtime" < "$timeout")); do
        if check_port_serving 8000; then
            return 0
        fi
        passtime=$(($(date +%s) - "$starttime"))
    done
    return 1
}

function start_adrenaline_environemnt() {
    if [ -z "$HF_HUB_OFFLINE" ]; then
        echo "HF_HUB_OFFLINE is not set. Please set it globaly to 1."
        exit 1
    fi
    start_adrenaline_tmux_session

    start_adrenaline_instances_in_tmux
    wait_adrenaline_instances_running
    while ! wait_adrenaline_instances_ready; do
        echo "Some instances have failed to start. Restarting..."
        kill_openai_servers
        wait_adrenaline_instances_fin
        sleep 5
        start_adrenaline_instances_in_tmux
        wait_adrenaline_instances_running
    done

    start_adrenaline_proxy_in_tmux
    wait_adrenaline_proxy_ready
}

function stop_adrenaline_environment() {
    kill_openai_servers
    kill_adrenaline_proxy_server
    wait_adrenaline_instances_fin
    kill_adrenaline_tmux_session
}
