#!/bin/bash
# This file demonstrates the example usage of disaggregated prefilling
# We will launch 2 vllm instances (1 for prefill and 1 for decode),
# and then transfer the KV cache between them.

VLLM_HOST_IP=${VLLM_HOST_IP:=$(hostname -I | awk '{print $1}')}
export VLLM_HOST_IP
VLLM_PORT=${VLLM_PORT:=12345}
export VLLM_PORT

OFFLOAD_RATIO=${OFFLOAD_RATIO:=0.6}
OFFLOAD_MAX_CAPTURE_BATCH=${OFFLOAD_MAX_CAPTURE_BATCH:=56}
MODEL=${MODEL:=meta-llama/Llama-2-7b-hf}
LOAD_FORMAT=${LOAD_FORMAT:=auto}
MAX_MODEL_LEN=${MAX_MODEL_LEN:=4096}

if [ "$MAX_MODEL_LEN" -gt 8192 ]; then
    MAX_SEQ_LEN_TO_CAPTURE=$MAX_MODEL_LEN
else
    MAX_SEQ_LEN_TO_CAPTURE=""
fi

# decoding instance, which is the KV consumer
VLLM_WORKER_MULTIPROC_METHOD=spawn \
VLLM_ATTENTION_BACKEND=ADRENALINE_FLASHINFER \
VLLM_LOGGING_LEVEL=DEBUG \
VLLM_ENABLE_KV_OFFLOAD=1 \
VLLM_OFFLOAD_RATIO="${OFFLOAD_RATIO}" \
VLLM_OFFLOAD_MAX_CAPTURE_BATCH="${OFFLOAD_MAX_CAPTURE_BATCH}" \
VLLM_DISTRIBUTED_KV_ROLE=consumer \
VLLM_PREFILL_INSTANCES=2 \
VLLM_RANK_IN_INSTANCES=2 \
CUDA_VISIBLE_DEVICES=2 \
python3 \
    -m vllm.entrypoints.openai.api_server \
    --disable-log-stats \
    --model "${MODEL}" \
    --load-format "${LOAD_FORMAT}" \
    --max-model-len "${MAX_MODEL_LEN}" \
    --max-seq-len-to-capture ${MAX_SEQ_LEN_TO_CAPTURE} \
    --port 8300 \
    --preemption-mode swap \
    --swap-space 10 \
    --gpu-memory-utilization 0.8
