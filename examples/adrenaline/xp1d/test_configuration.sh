#!/bin/bash
# Test script to verify the configurable xP1D functionality
# This script tests the device distribution and port assignment logic

# Copy the distribute_cuda_devices function to test it independently
function distribute_cuda_devices() {
    local available_devices="${AVAILABLE_CUDA_DEVICES:-0,1,2,3,4,5,6,7}"
    local prefill_instances="${PREFILL_INSTANCES:-2}"
    
    # Check if explicit device assignments are provided
    if [[ -n "${PREFILL_CUDA_DEVICES_EXPLICIT}" ]]; then
        # Use explicit assignments for prefill instances
        IFS=',' read -ra PREFILL_CUDA_DEVICES <<< "$PREFILL_CUDA_DEVICES_EXPLICIT"
        echo "Using explicit prefill device assignments: $PREFILL_CUDA_DEVICES_EXPLICIT"
    else
        # Convert comma-separated string to array
        IFS=',' read -ra DEVICE_ARRAY <<< "$available_devices"
        local total_devices=${#DEVICE_ARRAY[@]}
        
        # Distribute devices to prefill instances
        PREFILL_CUDA_DEVICES=()
        for ((i=0; i<prefill_instances; i++)); do
            local device_idx=$((i % total_devices))
            PREFILL_CUDA_DEVICES[i]=${DEVICE_ARRAY[device_idx]}
        done
    fi
    
    # Handle attention device assignment
    if [[ -n "${ATTENTION_CUDA_DEVICES_EXPLICIT}" ]]; then
        IFS=',' read -ra ATTENTION_CUDA_DEVICES <<< "$ATTENTION_CUDA_DEVICES_EXPLICIT"
        echo "Using explicit attention device assignments: $ATTENTION_CUDA_DEVICES_EXPLICIT"
    else
        # Use same devices as prefill instances for attention instances
        ATTENTION_CUDA_DEVICES=("${PREFILL_CUDA_DEVICES[@]}")
    fi
    
    # Handle decode device assignment
    if [[ -n "${DECODE_CUDA_DEVICE_EXPLICIT}" ]]; then
        DECODE_CUDA_DEVICE="$DECODE_CUDA_DEVICE_EXPLICIT"
        echo "Using explicit decode device assignment: $DECODE_CUDA_DEVICE_EXPLICIT"
    else
        # Convert comma-separated string to array for automatic assignment
        IFS=',' read -ra DEVICE_ARRAY <<< "$available_devices"
        local total_devices=${#DEVICE_ARRAY[@]}
        # Assign device to decode instance (use the next available device or wrap around)
        local decode_device_idx=$((prefill_instances % total_devices))
        DECODE_CUDA_DEVICE=${DEVICE_ARRAY[decode_device_idx]}
    fi
    
    # Export for use in sub-scripts
    export PREFILL_CUDA_DEVICES
    export ATTENTION_CUDA_DEVICES
    export DECODE_CUDA_DEVICE
    
    echo "CUDA Device Distribution:"
    echo "Available devices: $available_devices"
    echo "Prefill instances: $prefill_instances"
    for ((i=0; i<prefill_instances; i++)); do
        echo "  Prefill instance $i: GPU ${PREFILL_CUDA_DEVICES[i]}"
    done
    for ((i=0; i<prefill_instances; i++)); do
        echo "  Attention instance $i: GPU ${ATTENTION_CUDA_DEVICES[i]}"
    done
    echo "  Decode instance: GPU $DECODE_CUDA_DEVICE"
}

function test_port_assignment() {
    local prefill_instances=$1
    echo "Port Assignment for PREFILL_INSTANCES=$prefill_instances:"
    
    # Prefill ports
    for ((i=0; i<prefill_instances; i++)); do
        local port=$((8100 + i * 100))
        echo "  Prefill instance $i: port $port"
    done
    
    # Decode port
    local decode_port=$((8100 + prefill_instances * 100))
    echo "  Decode instance: port $decode_port"
    
    # Attention ports
    for ((i=0; i<prefill_instances; i++)); do
        local attention_port=$((8100 + (prefill_instances + 1 + i) * 100))
        echo "  Attention instance $i: port $attention_port"
    done
    
    echo "  Proxy server: port 8000"
    echo
}

echo "=== Testing Configurable xP1D Configuration ==="
echo

# Test 1: Default configuration
echo "Test 1: Default configuration (2 prefill instances)"
unset AVAILABLE_CUDA_DEVICES PREFILL_CUDA_DEVICES_EXPLICIT ATTENTION_CUDA_DEVICES_EXPLICIT DECODE_CUDA_DEVICE_EXPLICIT
PREFILL_INSTANCES=2
distribute_cuda_devices
test_port_assignment 2
echo

# Test 2: 4 prefill instances with custom devices
echo "Test 2: 4 prefill instances with custom devices"
AVAILABLE_CUDA_DEVICES="0,2,4,6"
PREFILL_INSTANCES=4
unset PREFILL_CUDA_DEVICES_EXPLICIT ATTENTION_CUDA_DEVICES_EXPLICIT DECODE_CUDA_DEVICE_EXPLICIT
distribute_cuda_devices
test_port_assignment 4
echo

# Test 3: Explicit device assignment
echo "Test 3: Explicit device assignment (3 prefill instances)"
PREFILL_CUDA_DEVICES_EXPLICIT="1,3,5"
ATTENTION_CUDA_DEVICES_EXPLICIT="2,4,6"
DECODE_CUDA_DEVICE_EXPLICIT="7"
PREFILL_INSTANCES=3
distribute_cuda_devices
test_port_assignment 3
echo

# Test 4: Large scale setup
echo "Test 4: Large scale setup (8 prefill instances)"
AVAILABLE_CUDA_DEVICES="0,1,2,3,4,5,6,7"
PREFILL_INSTANCES=8
unset PREFILL_CUDA_DEVICES_EXPLICIT ATTENTION_CUDA_DEVICES_EXPLICIT DECODE_CUDA_DEVICE_EXPLICIT
distribute_cuda_devices
test_port_assignment 8
echo

# Test 5: Single prefill instance
echo "Test 5: Single prefill instance"
AVAILABLE_CUDA_DEVICES="0,1,2,3"
PREFILL_INSTANCES=1
unset PREFILL_CUDA_DEVICES_EXPLICIT ATTENTION_CUDA_DEVICES_EXPLICIT DECODE_CUDA_DEVICE_EXPLICIT
distribute_cuda_devices
test_port_assignment 1
echo

echo "=== All configuration tests completed ==="
