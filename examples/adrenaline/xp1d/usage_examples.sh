#!/bin/bash
# Usage examples for the configurable xP1D adrenaline scripts

echo "=== Configurable xP1D Adrenaline Usage Examples ==="
echo
echo "The adrenaline_instances_start.sh script now supports configurable xP1D setup."
echo "Here are various ways to use it:"
echo

echo "1. Default behavior (2 prefill + 1 decode + 2 attention instances):"
echo "   ./adrenaline_instances_start.sh"
echo

echo "2. Specify number of prefill instances:"
echo "   PREFILL_INSTANCES=3 ./adrenaline_instances_start.sh"
echo

echo "3. Specify available GPUs for automatic distribution:"
echo "   PREFILL_INSTANCES=4 AVAILABLE_CUDA_DEVICES=\"0,1,2,3,4,5\" ./adrenaline_instances_start.sh"
echo

echo "4. Explicit device assignment for all instances:"
echo "   PREFILL_INSTANCES=2 PREFILL_CUDA_DEVICES_EXPLICIT=\"0,1\" ATTENTION_CUDA_DEVICES_EXPLICIT=\"0,1\" DECODE_CUDA_DEVICE_EXPLICIT=\"2\" ./adrenaline_instances_start.sh"
echo

echo "5. With custom model and parameters:"
echo "   PREFILL_INSTANCES=3 MODEL=\"meta-llama/Llama-2-13b-hf\" OFFLOAD_RATIO=0.8 AVAILABLE_CUDA_DEVICES=\"0,1,2,3\" ./adrenaline_instances_start.sh"
echo

echo "6. Large scale setup (8 prefill instances):"
echo "   PREFILL_INSTANCES=8 AVAILABLE_CUDA_DEVICES=\"0,1,2,3,4,5,6,7\" ./adrenaline_instances_start.sh"
echo

echo "7. With custom block configurations:"
echo "   PREFILL_INSTANCES=4 NUM_PREFILL_BLOCKS=1024 NUM_ATTENTION_BLOCKS=8000 ./adrenaline_instances_start.sh"
echo

echo "=== Environment Variables Summary ==="
echo
echo "Core Configuration:"
echo "PREFILL_INSTANCES             - Number of prefill instances (default: 2)"
echo "AVAILABLE_CUDA_DEVICES        - Comma-separated list of GPU IDs for automatic distribution"
echo "PREFILL_CUDA_DEVICES_EXPLICIT - Comma-separated list of GPU IDs for prefill instances (explicit)"
echo "ATTENTION_CUDA_DEVICES_EXPLICIT - Comma-separated list of GPU IDs for attention instances (explicit)"
echo "DECODE_CUDA_DEVICE_EXPLICIT   - Single GPU ID for decode instance (explicit)"
echo
echo "Model Configuration:"
echo "MODEL                         - Model name (default: meta-llama/Llama-2-7b-hf)"
echo "MAX_MODEL_LEN                 - Maximum model length (default: 4096)"
echo "LOAD_FORMAT                   - Model load format (default: auto)"
echo
echo "Performance Configuration:"
echo "OFFLOAD_RATIO                 - KV cache offload ratio (default: 0.6)"
echo "NUM_PREFILL_BLOCKS            - Number of GPU blocks for prefill instances (default: 512)"
echo "NUM_ATTENTION_BLOCKS          - Number of GPU blocks for attention instances (default: 5000)"
echo "OFFLOAD_BACKEND               - Offload backend type (default: loadaware)"
echo
echo "=== Port Assignment Logic ==="
echo
echo "Ports are automatically assigned based on instance type and index:"
echo "- Prefill instances: 8100 + (instance_index * 100)"
echo "- Decode instance: 8100 + (prefill_instances * 100)"
echo "- Attention instances: 8100 + ((prefill_instances + 1 + instance_index) * 100)"
echo "- Proxy server: 8000 (fixed)"
echo
echo "Example with PREFILL_INSTANCES=3:"
echo "- Prefill 0: port 8100"
echo "- Prefill 1: port 8200"
echo "- Prefill 2: port 8300"
echo "- Decode: port 8400"
echo "- Attention 0: port 8500"
echo "- Attention 1: port 8600"
echo "- Attention 2: port 8700"
echo "- Proxy: port 8000"
echo
echo "=== CUDA Device Assignment Logic ==="
echo
echo "Automatic distribution (when AVAILABLE_CUDA_DEVICES is set):"
echo "1. Prefill instances get devices in round-robin fashion"
echo "2. Attention instances share the same devices as prefill instances"
echo "3. Decode instance gets the next available device"
echo
echo "Explicit assignment takes precedence over automatic distribution."
echo "Make sure the number of explicit devices matches the number of instances."
