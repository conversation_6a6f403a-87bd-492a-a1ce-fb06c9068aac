# Configurable xP1D Adrenaline Scripts

This directory contains configurable scripts for launching xP1D (x prefill instances + 1 decode instance) adrenaline setups with dynamic CUDA device allocation and port assignment.

## Overview

The xP1D scripts extend the original 2P1D setup to support any number of prefill instances, making it easy to scale horizontally based on your hardware configuration and performance requirements.

### Architecture

- **x Prefill Instances**: Handle initial token processing (KV producers)
- **1 Decode Instance**: Handles token generation (KV consumer)  
- **x Attention Instances**: Handle KV cache offloading (one per prefill instance)
- **1 Proxy Server**: Coordinates requests between all instances

## Files

- `adrenaline_instances_start.sh` - Main orchestration script
- `prefill_instance_start.sh` - Template for prefill instances
- `decode_instance_start.sh` - Decode instance script
- `attention_instance_start.sh` - Template for attention instances
- `proxy_instance_start.sh` - Proxy server script
- `usage_examples.sh` - Usage examples and documentation
- `test_configuration.sh` - Test script for verifying configuration logic

## Key Features

### 1. Configurable Instance Count
Set the number of prefill instances via `PREFILL_INSTANCES` environment variable:
```bash
PREFILL_INSTANCES=4 ./adrenaline_instances_start.sh
```

### 2. Automatic CUDA Device Distribution
Specify available GPUs and let the script distribute them automatically:
```bash
AVAILABLE_CUDA_DEVICES="0,1,2,3,4,5" PREFILL_INSTANCES=3 ./adrenaline_instances_start.sh
```

### 3. Explicit Device Assignment
For fine-grained control, specify exact GPU assignments:
```bash
PREFILL_CUDA_DEVICES_EXPLICIT="0,2,4" \
ATTENTION_CUDA_DEVICES_EXPLICIT="1,3,5" \
DECODE_CUDA_DEVICE_EXPLICIT="6" \
PREFILL_INSTANCES=3 ./adrenaline_instances_start.sh
```

### 4. Dynamic Port Assignment
Ports are automatically calculated based on instance count and type:
- Prefill instances: `8100 + (instance_index * 100)`
- Decode instance: `8100 + (prefill_instances * 100)`
- Attention instances: `8100 + ((prefill_instances + 1 + instance_index) * 100)`
- Proxy server: `8000` (fixed)

## Environment Variables

### Core Configuration
- `PREFILL_INSTANCES` - Number of prefill instances (default: 2)
- `AVAILABLE_CUDA_DEVICES` - Comma-separated GPU IDs for automatic distribution
- `PREFILL_CUDA_DEVICES_EXPLICIT` - Explicit GPU assignments for prefill instances
- `ATTENTION_CUDA_DEVICES_EXPLICIT` - Explicit GPU assignments for attention instances
- `DECODE_CUDA_DEVICE_EXPLICIT` - Explicit GPU assignment for decode instance

### Model Configuration
- `MODEL` - Model name (default: meta-llama/Llama-2-7b-hf)
- `MAX_MODEL_LEN` - Maximum model length (default: 4096)
- `LOAD_FORMAT` - Model load format (default: auto)

### Performance Configuration
- `OFFLOAD_RATIO` - KV cache offload ratio (default: 0.6)
- `NUM_PREFILL_BLOCKS` - GPU blocks for prefill instances (default: 512)
- `NUM_ATTENTION_BLOCKS` - GPU blocks for attention instances (default: 5000)
- `OFFLOAD_BACKEND` - Offload backend type (default: loadaware)

## Usage Examples

### Basic Usage
```bash
# Default: 2 prefill + 1 decode + 2 attention instances
./adrenaline_instances_start.sh

# Custom number of prefill instances
PREFILL_INSTANCES=4 ./adrenaline_instances_start.sh

# With custom model
MODEL="meta-llama/Llama-2-13b-hf" PREFILL_INSTANCES=3 ./adrenaline_instances_start.sh
```

### Advanced Configuration
```bash
# Large scale setup with 8 prefill instances
PREFILL_INSTANCES=8 \
AVAILABLE_CUDA_DEVICES="0,1,2,3,4,5,6,7" \
NUM_PREFILL_BLOCKS=1024 \
NUM_ATTENTION_BLOCKS=8000 \
./adrenaline_instances_start.sh

# Explicit device assignment
PREFILL_INSTANCES=3 \
PREFILL_CUDA_DEVICES_EXPLICIT="0,2,4" \
ATTENTION_CUDA_DEVICES_EXPLICIT="1,3,5" \
DECODE_CUDA_DEVICE_EXPLICIT="6" \
./adrenaline_instances_start.sh
```

## Testing

Run the test script to verify configuration logic:
```bash
./test_configuration.sh
```

View usage examples:
```bash
./usage_examples.sh
```

## Migration from 2P1D

The xP1D scripts are backward compatible with 2P1D setups. Simply set `PREFILL_INSTANCES=2` or use the default value to get the same behavior as the original 2P1D scripts, but with the added flexibility of configurable device allocation.

## Differences from distributed_kv/xp1d

While inspired by the distributed_kv/xp1d scripts, the adrenaline xp1d scripts include:
- Attention instances for KV cache offloading
- Different environment variable names and roles
- Adrenaline-specific configurations (OFFLOAD_RATIO, attention blocks, etc.)
- Integration with the adrenaline proxy server
