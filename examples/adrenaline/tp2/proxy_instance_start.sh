#!/bin/bash
# This file demonstrates the example usage of disaggregated prefilling
# We will launch 2 vllm instances (1 for prefill and 1 for decode),
# and then transfer the KV cache between them.

VLLM_HOST_IP=${VLLM_HOST_IP:=$(hostname -I | awk '{print $1}')}
export VLLM_HOST_IP
VLLM_PORT=${VLLM_PORT:=12345}
export VLLM_PORT

OFFLOAD_RATIO=${OFFLOAD_RATIO:=0.6}
MODEL=${MODEL:=meta-llama/Llama-2-7b-hf}
OFFLOAD_BACKEND=${OFFLOAD_BACKEND:=loadaware}

# launch a proxy server that opens the service at port 8000
python -m adrenaline.entrypoints.adrenaline_proxy_server \
    --tokenizer "${MODEL}" \
    --offload-backend "${OFFLOAD_BACKEND}" \
    --offload-ratio "${OFFLOAD_RATIO}" \
    --num-decode-blocks "${NUM_DECODE_BLOCKS}" \
    --num-attention-blocks "${NUM_ATTENTION_BLOCKS}"
