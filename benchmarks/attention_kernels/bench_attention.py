import argparse
import torch
import flashinfer

from vllm.config import LoadFormat
from vllm.engine.arg_utils import EngineArgs
from vllm.utils import cdiv

from adrenaline.utils import input_factory
from adrenaline.utils.profile_utils import profile


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--avg-seq-len", type=int, default=128)
    parser.add_argument("--batch-size", type=int, default=3)
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    engine_args = EngineArgs(
        "meta-llama/Llama-2-7b-hf",
        load_format=LoadFormat.DUMMY,
    )
    config = engine_args.create_engine_config()

    num_layers = config.model_config.get_num_layers(config.parallel_config)
    num_qo_heads = config.model_config.get_num_attention_heads(config.parallel_config)
    num_kv_heads = config.model_config.get_num_kv_heads(config.parallel_config)
    head_dim = config.model_config.get_head_size()
    block_size = config.cache_config.block_size

    batch_size = args.batch_size
    avg_seq_len = args.avg_seq_len
    seq_lens = input_factory.create_seq_lens(batch_size, avg_seq_len, config=config)
    block_lens = [cdiv(seq_len, block_size) for seq_len in seq_lens]

    max_num_pages = sum(block_lens)
    prefix_block_lens = [sum(block_lens[:i]) for i in range(len(block_lens) + 1)]
    workspace_buffer = torch.empty(128 * 1024 * 1024, dtype=torch.uint8, device="cuda")
    decode_wrapper = flashinfer.BatchDecodeWithPagedKVCacheWrapper(
        workspace_buffer, "NHD"
    )
    kv_page_indices = torch.arange(max_num_pages, dtype=torch.int32, device="cuda")
    kv_page_indptr = torch.tensor(
        prefix_block_lens, dtype=torch.int32, device="cuda"
    )
    # 1 <= kv_last_page_len <= page_size
    kv_last_page_len = torch.randint(
        1, block_size + 1, (batch_size,), dtype=torch.int32, device="cuda"
    )
    kv_cache_at_layer = [
        torch.randn(
            max_num_pages,
            2,
            block_size,
            num_kv_heads,
            head_dim,
            dtype=torch.float16,
            device="cuda",
        )
        for _ in range(num_layers)
    ]
    decode_wrapper.plan(
        kv_page_indptr,
        kv_page_indices,
        kv_last_page_len,
        num_qo_heads,
        num_kv_heads,
        head_dim,
        block_size,
        pos_encoding_mode="NONE",
        data_type=torch.float16
    )
    outputs = []
    with profile():
        for i in range(num_layers):
            q = torch.randn(batch_size, num_qo_heads, head_dim, dtype=torch.half, device="cuda")
            kv_cache = kv_cache_at_layer[i]
            o = decode_wrapper.run(q, kv_cache)
            outputs.append(o)

    outputs[0].shape
