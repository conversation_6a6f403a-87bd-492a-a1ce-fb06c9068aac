#!/bin/bash

function profile_flashinfer_bandwidth_with_mps() {
    local pcts=()
    for pct in $(seq 10 5 20); do
        pcts+=("${pct}")
    done
    for pct in $(seq 30 10 100); do
        pcts+=("${pct}")
    done
    local seqlens=(256 512 1024 2048)
    local batches=(8 16 32)

    local output_dir="outputs/nsys-rep/flashinfer"
    if [[ ! -d "${output_dir}" ]]; then
        mkdir -p "${output_dir}"
    fi
    for pct in "${pcts[@]}"; do
        for batch in "${batches[@]}"; do
            for seqlen in "${seqlens[@]}"; do
                CUDA_MPS_ACTIVE_THREAD_PERCENTAGE=${pct} \
                    nsys profile --gpu-metrics-devices=all --gpu-metrics-frequency=100000 \
                    --capture-range=cudaProfilerApi --capture-range-end=stop \
                    -o "${output_dir}"/flashinfer_sm_"${pct}"_seqlen_"${seqlen}"_batch_"${batch}".nsys-rep -f true \
                    python -m benchmarks.attention_kernels.bench_flashinfer \
                        --avg-seq-len "${seqlen}" --batch-size "${batch}"
            done
        done
    done
}

profile_flashinfer_bandwidth_with_mps
