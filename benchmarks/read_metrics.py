import csv

def aggregate_metrics(filename: str, print_data: bool = False):
    with open(filename) as f:
        reader = csv.DictReader(f)
        total_time = 0.0
        total_calc = 0.0
        total_bandwidth = 0.0
        for row in reader:
            op_time = float(row["time(us)"])
            total_time += op_time
            total_calc += float(row["calc"]) * op_time
            total_bandwidth += float(row["bandwidth"]) * op_time
            if print_data:
                print(row)
        avg_calc = total_calc / total_time
        avg_bandwidth = total_bandwidth / total_time
        return avg_calc, avg_bandwidth


if __name__ == "__main__":
    input_len = 1024
    batch = 49
    filenames = [
        f"benchmarks/disagg_benchmarks/metrics/input_{input_len}_batch_{batch}_prefill.csv",
        f"benchmarks/disagg_benchmarks/metrics/input_{input_len}_batch_{batch}_decode.csv",
    ]
    prefill_calc, prefill_bandwidth = aggregate_metrics(filenames[0], print_data=True)
    print(f"{prefill_calc=:.3f}, {prefill_bandwidth=:.3f}")
    print() 
    decode_calc, decode_bandwidth = aggregate_metrics(filenames[1], print_data=True)
    print(f"{decode_calc=:.3f}, {decode_bandwidth=:.3f}")
