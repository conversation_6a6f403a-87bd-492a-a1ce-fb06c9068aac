import argparse
import os
import torch

from torch import multiprocessing
from torch import distributed as dist
from typing import Union
from vllm.attention.selector import backend_name_to_enum, global_force_attn_backend
from vllm.config import VllmConfig, LoadFormat
from vllm.distributed.kv_transfer import vllm_adapter as dist_kv
from vllm.distributed import parallel_state
from vllm.distributed.kv_transfer.agent.offload_exec_agent import Offload_attn_exec_agent
from vllm.engine.arg_utils import EngineArgs
from vllm.forward_context import set_forward_context
from vllm.model_executor.utils import set_random_seed
from vllm.utils import (
    update_environment_variables,
    get_distributed_init_method,
    get_ip,
    get_open_port,
)
from vllm.worker.model_runner import ModelInputForGPUWithSamplingMetadata

from adrenaline.attention.backends import flashinfer
from adrenaline.model_loader.models.offload_attn import OffloadAttn
from adrenaline.model_runner.attn_runner import AttentionRunner
from adrenaline.model_runner.model_runner import ModelRunner
from adrenaline.utils import input_factory
from adrenaline.utils.profile_utils import profile


def init_parallel_groups(rank: int, world_size: int, init_method: str):
    dist.init_process_group(
        backend=dist.Backend.GLOO,
        rank=rank,
        world_size=world_size,
        init_method=init_method,
    )
    parallel_state._PP = parallel_state.init_model_parallel_group(
        [[0], [1]], 0, dist.Backend.NCCL, use_custom_allreduce=False, group_name="pp"
    )
    parallel_state._TP = parallel_state.init_model_parallel_group(
        [[0], [1]], 0, dist.Backend.NCCL, use_message_queue_broadcaster=True, group_name="tp"
    )
    parallel_state._OFFLOAD_EXEC = Offload_attn_exec_agent([[0, 1]], 0, decode_master_rank=0)


def destroy_parallel_groups():
    parallel_state.destroy_model_parallel()
    dist.destroy_process_group()


def execute_model(
    model_runner: Union[ModelRunner, AttentionRunner],
    model_input: ModelInputForGPUWithSamplingMetadata,
    kv_caches: list[torch.Tensor],
):
    if dist_kv.IS_ATTN_EXECUTOR:
        model_runner.attn_state.begin_forward(model_input)
        with set_forward_context(model_input.attn_metadata):
            assert isinstance(model_input.attn_metadata, flashinfer.AdrenalineFlashInferMetadata)
            assert isinstance(model_runner.model, OffloadAttn)
            if model_input.attn_metadata.use_cuda_graph:
                batch = model_input.attn_metadata.num_offloads_padded_for_graph
            else:
                batch = model_input.attn_metadata.num_offloads
            for layer_idx in range(model_runner.model.start_layer, model_runner.model.end_layer):
                qkv = torch.rand(
                    batch, model_runner.model.qkv_size, dtype=model_runner.model.model_dtype, device=model_input.attn_metadata.device
                )
                q, k, v = qkv.split([model_runner.model.q_size, model_runner.model.kv_size, model_runner.model.kv_size], dim=-1)
                model_runner.model.model.forward_with_blk(
                    q,
                    k,
                    v,
                    kv_caches[layer_idx - model_runner.model.start_layer],
                    model_input.attn_metadata,
                    attn_sm=32,
                )

def workser_task(
    config: VllmConfig,
    input_len: int = 1024,
    max_output_len: int = 128,
    decode_batch: int = 3,
    offload_pct: float = 0,
):
    attn_backend_name = flashinfer.AdrenalineFlashInferBackend.get_name()
    global_force_attn_backend(backend_name_to_enum(attn_backend_name))

    block_size = config.cache_config.block_size
    num_gpu_blocks = (input_len + max_output_len) * (decode_batch + 1) // block_size
    config.cache_config.num_gpu_blocks = num_gpu_blocks
    if dist_kv.IS_KV_CONSUMER:
        model_runner = ModelRunner(vllm_config=config, is_driver_worker=True)
    else:
        assert dist_kv.IS_ATTN_EXECUTOR
        model_runner = AttentionRunner(vllm_config=config, is_driver_worker=True)

    model_runner.load_model()
    kv_caches = input_factory.build_kv_caches(config, num_gpu_blocks, "FLASHINFER")
    if not config.model_config.enforce_eager:
        kv_caches_for_capture = [kv_caches]
        model_runner.capture_model(kv_caches_for_capture)
    set_random_seed(0)

    num_offloads = int(decode_batch * offload_pct)
    model_input = input_factory.prepare_decode_model_runner_input(
        config,
        model_runner,
        decode_batch,
        input_len=input_len,
        max_output_len=max_output_len,
        num_offloads=num_offloads,
        num_gpu_blocks=num_gpu_blocks,
    )

    assert model_input is not None
    assert isinstance(model_input.attn_metadata, flashinfer.AdrenalineFlashInferMetadata)

    num_warmups = 2
    for _ in range(num_warmups):
        execute_model(model_runner, model_input, kv_caches)

    num_execs = 3
    dist.barrier()
    with profile():
        dist.barrier()
        for _ in range(num_execs):
            execute_model(model_runner, model_input, kv_caches)
        dist.barrier()


def worker_fn(
    rank: int,
    world_size: int,
    dist_init_method: str,
    config: VllmConfig,
    input_len: int = 1024,
    max_output_len: int = 128,
    decode_batch: int = 3,
    offload_pct: float = 0,
):
    init_parallel_groups(rank, world_size, dist_init_method)
    workser_task(config, input_len, max_output_len, decode_batch, offload_pct)
    # destroy_parallel_groups()


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--input-len", type=int, default=1024)
    parser.add_argument("--output-len", type=int, default=128)
    parser.add_argument("--decode-batch", type=int, default=3)
    parser.add_argument("--offload-pct", type=float, default=1)
    parser.add_argument("--enforce-eager", action="store_true")
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    print(args)
    engine_args = EngineArgs(
        "meta-llama/Llama-2-7b-hf",
        load_format=LoadFormat.DUMMY,
        enforce_eager=args.enforce_eager,
    )
    config = engine_args.create_engine_config()
    ctx = multiprocessing.get_context("spawn")
    roles = ["consumer", "offload"]
    world_size = len(roles)
    devices = os.environ.get("CUDA_VISIBLE_DEVICES", "1,0").split(",")
    assert len(devices) == world_size
    dist_init_method = get_distributed_init_method(get_ip(), get_open_port())
    processes_args = [
        (
            rank,
            world_size,
            dist_init_method,
            config,
            args.input_len,
            args.output_len,
            args.decode_batch,
            args.offload_pct,
        )
        for rank in range(world_size)
    ]
    processes = [ctx.Process(target=worker_fn, args=args) for args in processes_args]
    assert "CUDA_MPS_ENABLE_PER_CTX_DEVICE_MULTIPROCESSOR_PARTITIONING" in os.environ.keys()
    for p, role, device in zip(processes, roles, devices):
        env_dicts = {
            "VLLM_ATTENTION_BACKEND": "ADRENALINE_FLASHINFER",
            "VLLM_DISTRIBUTED_KV_ROLE": role,
            "CUDA_VISIBLE_DEVICES": device,
            "VLLM_LOGGING_LEVEL": "DEBUG",
            "VLLM_ENABLE_KV_OFFLOAD": "1",
        }
        if role == "offload":
            env_dicts["CUDA_MPS_ACTIVE_THREAD_PERCENTAGE"] = str(30)
        else:
            env_dicts["CUDA_MPS_ACTIVE_THREAD_PERCENTAGE"] = str(100)
        update_environment_variables(env_dicts)
        p.start()
    [p.join(timeout=90) for p in processes]
