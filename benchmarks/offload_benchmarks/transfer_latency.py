import argparse
import contextlib
import torch

from typing import Optional
from vllm.utils import get_distributed_init_method
from adrenaline.utils.profile_utils import profile


@contextlib.contextmanager
def init_dist_env(rank: int, world_size: int, dist_init_method: str):
    print(f"initializing distributed group ({rank=})")
    torch.distributed.init_process_group(
        backend=torch.distributed.Backend.NCCL,
        init_method=dist_init_method,
        world_size=world_size,
        rank=rank,
    )
    print(f"initialized! My rank is {rank}")
    yield
    torch.distributed.destroy_process_group()


class CudaTimer():
    def __init__(
        self,
        start: Optional[torch.cuda.Event] = None,
        end: Optional[torch.cuda.Event] = None,
    ):
        self.start: torch.cuda.Event = start or torch.cuda.Event(enable_timing=True)
        self.end: torch.cuda.Event = end or torch.cuda.Event(enable_timing=True)

    def __enter__(self):
        self.start.record()

    def __exit__(self, exc_type, exc_obj, exc_tb):
        self.end.record()

    def get_record_time(self):
        """
        return the record time (ms).
        """
        assert self.start.query()
        assert self.end.query()
        return self.start.elapsed_time(self.end)


def transfer_task(size, op: str = "scatter"):
    if op == "scatter":
        output_tensor = torch.empty(size, device="cuda", dtype=torch.half)
        if torch.distributed.get_rank() == 0:
            t0 = torch.empty_like(output_tensor)
            t1 = torch.empty_like(output_tensor)
            torch.distributed.scatter(output_tensor, [t0, t1], src=0)
        else:
            assert torch.distributed.get_rank() == 1
            torch.distributed.scatter(output_tensor, None, src=0)
    elif op == "send_recv":
        tensor = torch.empty(size, device="cuda", dtype=torch.half)
        if torch.distributed.get_rank() == 0:
            torch.distributed.send(tensor, dst=1)
        else:
            torch.distributed.recv(tensor, src=0)
    elif op == "broadcast":
        tensor = torch.empty(size, device="cuda", dtype=torch.half)
        torch.distributed.broadcast(tensor, src=0)



def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--ip", type=str, default="localhost")
    parser.add_argument("--port", type=int, default=12345)
    parser.add_argument("--rank", type=int, default=0)
    parser.add_argument("--batch", type=int, default=64)
    parser.add_argument("--op", type=str, default="send_recv")
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    print(args)
    ip: str = args.ip
    port: int = args.port
    rank: int = args.rank
    batch: int = args.batch
    op: str = args.op
    world_size = 2
    dist_init_method = get_distributed_init_method(ip, port)
    with init_dist_env(rank, world_size, dist_init_method):
        timer = CudaTimer()
        transfer_task((batch * 3, 4096), op)
        transfer_task((batch * 3, 4096), op)
        transfer_task((batch * 3, 4096), op)
        times = 32
        with profile():
            torch.cuda._sleep(1000000000)
            torch.cuda._sleep(1000000000)
            with timer:
                for _ in range(times):
                    transfer_task((batch * 3, 4096), op)
            torch.cuda.synchronize()
        dur = timer.get_record_time() / times / 1e3  # second
        t = torch.empty(batch * 3, 4096, device="cuda", dtype=torch.half)
        size = t.element_size() * t.numel() / 1e9  # GB
        bandwidth = size / dur
        print(f"{size=}GB, {dur=}s, {bandwidth=:.2f}GB/s")
        torch.distributed.barrier()
