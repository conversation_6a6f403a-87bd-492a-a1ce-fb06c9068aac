import argparse
import os
import torch

from torch import multiprocessing
from torch import distributed as dist
from typing import Union
from vllm.attention.selector import backend_name_to_enum, global_force_attn_backend
from vllm.config import VllmConfig, LoadFormat
from vllm.distributed.kv_transfer import vllm_adapter as dist_kv
from vllm.distributed import parallel_state
from vllm.distributed.kv_transfer.agent.kv_transfer import KV_transfer_agent
from vllm.distributed.kv_transfer.agent.offload_exec_agent import Offload_attn_exec_agent
from vllm.distributed.kv_transfer.agent.kv_transfer_in_device import KV_transfer_agent_in_same_device
from vllm.distributed.kv_transfer.kv_lookup_buffer.simple_buffer import SimpleKVLookupBuffer
from vllm.engine.arg_utils import EngineArgs
from vllm.model_executor.utils import set_random_seed
from vllm.utils import (
    update_environment_variables,
    remove_environment_vatiables,
    get_distributed_init_method,
    get_ip,
    get_open_port,
    cdiv,
)
from vllm.worker.model_runner import ModelInputForGPUWithSamplingMetadata, _get_graph_batch_size

from adrenaline.attention.backends import flashinfer
from adrenaline.model_runner.attn_runner import AttentionRunner
from adrenaline.model_runner.model_runner import ModelRunner
from adrenaline.utils import input_factory
from adrenaline.utils.profile_utils import profile


def init_parallel_groups(rank: int, world_size: int, init_method: str):
    dist.init_process_group(
        backend=dist.Backend.GLOO,
        rank=rank,
        world_size=world_size,
        init_method=init_method,
    )
    parallel_state._PP = parallel_state.init_model_parallel_group(
        [[0], [1], [2]], 0, dist.Backend.NCCL, use_custom_allreduce=False, group_name="pp"
    )
    parallel_state._TP = parallel_state.init_model_parallel_group(
        [[0], [1], [2]], 0, dist.Backend.NCCL, use_message_queue_broadcaster=True, group_name="tp"
    )
    parallel_state._DISAGG = KV_transfer_agent([[[0, 1]]], 0)
    parallel_state._OFFLOAD_KV = KV_transfer_agent_in_same_device([[[0, 2]]], 0)
    parallel_state._OFFLOAD_EXEC = Offload_attn_exec_agent([[1, 2]], 0, decode_master_rank=1)


def destroy_parallel_groups():
    parallel_state.destroy_model_parallel()
    dist.destroy_process_group()


def execute_model(
    model_runner: Union[ModelRunner, AttentionRunner],
    model_input: ModelInputForGPUWithSamplingMetadata,
    kv_caches: list[torch.Tensor],
):
    if dist_kv.IS_KV_PRODUCER:
        assert isinstance(model_runner, ModelRunner)
        return model_runner.execute_model(model_input, kv_caches)
    elif dist_kv.IS_KV_CONSUMER:
        assert isinstance(model_runner, ModelRunner)
        return model_runner.execute_model(model_input, kv_caches)
    else:
        assert dist_kv.IS_ATTN_EXECUTOR
        if model_input is None:
            return None
        assert isinstance(model_runner, AttentionRunner)
        assert kv_caches is not None
        return model_runner.execute_model(model_input, kv_caches)


def clear_prefill_send_buffer():
    if dist_kv.IS_KV_PRODUCER:
        send_buf: SimpleKVLookupBuffer = parallel_state._DISAGG.send_buffer
        send_buf.clear()
        assert send_buf.buffer_size == 0


def worker_task(
    config: VllmConfig,
    input_len: int = 1024,
    max_output_len: int = 128,
    num_locals: int = 3,
    offload_ratio: float = 0,
    num_prefills: int = 1
):
    attn_backend_name = flashinfer.AdrenalineFlashInferBackend.get_name()
    global_force_attn_backend(backend_name_to_enum(attn_backend_name))

    block_size = config.cache_config.block_size
    max_len_per_req = input_len + max_output_len + 1
    num_offloads = int(num_locals * offload_ratio)
    if dist_kv.IS_KV_PRODUCER:
        model_runner = ModelRunner(vllm_config=config, is_driver_worker=True)
        num_gpu_blocks = cdiv(max_len_per_req, block_size) * num_prefills
    elif dist_kv.IS_KV_CONSUMER:
        model_runner = ModelRunner(vllm_config=config, is_driver_worker=True)
        num_gpu_blocks = cdiv(max_len_per_req, block_size) * num_locals
    else:
        assert dist_kv.IS_ATTN_EXECUTOR
        model_runner = AttentionRunner(vllm_config=config, is_driver_worker=True)
        num_gpu_blocks = cdiv(max_len_per_req, block_size) * num_offloads

    model_runner.load_model()
    kv_caches = input_factory.build_kv_caches(config, num_gpu_blocks, "FLASHINFER")
    if not config.model_config.enforce_eager and (dist_kv.IS_KV_CONSUMER or dist_kv.IS_ATTN_EXECUTOR):
        model_runner.max_batchsize_to_capture = _get_graph_batch_size(num_locals)
        # used in capture graph (AttentionState.graph_capture)
        config.cache_config.num_gpu_blocks = num_gpu_blocks
        kv_caches_for_capture = [kv_caches]
        model_runner.capture_model(kv_caches_for_capture)
    dist.barrier()
    set_random_seed(0)

    if dist_kv.IS_KV_PRODUCER:
        model_input = input_factory.prepare_prefill_model_input(
            config, model_runner, num_prefills, input_len=input_len, num_gpu_blocks=num_gpu_blocks
        )
    else:
        model_input = input_factory.prepare_decode_model_runner_input(
            config,
            model_runner,
            num_locals + num_offloads,
            input_len=input_len,
            max_output_len=max_output_len,
            num_offloads=num_offloads,
            num_gpu_blocks=num_gpu_blocks,
        )

    if model_input is not None:
        assert isinstance(model_input.attn_metadata, flashinfer.AdrenalineFlashInferMetadata)

    num_warmups = 2
    for _ in range(num_warmups):
        execute_model(model_runner, model_input, kv_caches)
        clear_prefill_send_buffer()

    num_execs = 3
    dist.barrier()
    with profile():
        dist.barrier()
        for _ in range(num_execs):
            execute_model(model_runner, model_input, kv_caches)
            clear_prefill_send_buffer()
        dist.barrier()


def worker_fn(
    rank: int,
    world_size: int,
    dist_init_method: str,
    config: VllmConfig,
    input_len: int = 1024,
    max_output_len: int = 128,
    num_locals: int = 3,
    offload_ratio: float = 0,
):
    init_parallel_groups(rank, world_size, dist_init_method)
    worker_task(config, input_len, max_output_len, num_locals, offload_ratio)
    # destroy_parallel_groups()


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--model", type=str, default="meta-llama/Llama-2-7b-hf")
    parser.add_argument("--input-len", type=int, default=1024)
    parser.add_argument("--output-len", type=int, default=128)
    parser.add_argument("--num-locals", type=int, default=3)
    parser.add_argument("--offload-ratio", type=float, default=0)
    parser.add_argument("--enforce-eager", action="store_true")
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    print(args)
    engine_args = EngineArgs(
        args.model,
        load_format=LoadFormat.DUMMY,
        enforce_eager=args.enforce_eager,
    )
    config = engine_args.create_engine_config()
    ctx = multiprocessing.get_context("spawn")
    roles = ["producer", "consumer", "offload"]
    world_size = len(roles)
    devices = os.environ.get("CUDA_VISIBLE_DEVICES", "0,1").split(",")
    devices = devices + [devices[0]]
    assert len(devices) == world_size
    dist_init_method = get_distributed_init_method(get_ip(), get_open_port())
    processes_args = [
        (
            rank,
            world_size,
            dist_init_method,
            config,
            args.input_len,
            args.output_len,
            args.num_locals,
            args.offload_ratio,
        )
        for rank in range(world_size)
    ]
    processes = [ctx.Process(target=worker_fn, args=args) for args in processes_args]
    assert "CUDA_MPS_ENABLE_PER_CTX_DEVICE_MULTIPROCESSOR_PARTITIONING" in os.environ.keys()
    for p, role, device in zip(processes, roles, devices):
        env_dicts = {
            "VLLM_ATTENTION_BACKEND": "ADRENALINE_FLASHINFER",
            "VLLM_DISTRIBUTED_KV_ROLE": role,
            "CUDA_VISIBLE_DEVICES": device,
            "VLLM_LOGGING_LEVEL": "DEBUG",
            "VLLM_ENABLE_KV_OFFLOAD": "1",
            "VLLM_OFFLOAD_RATIO": f"{args.offload_ratio}",
        }
        if role == "producer":
            env_dicts["CUDA_MPS_ACTIVE_THREAD_PERCENTAGE"] = str(70)
        elif role == "offload":
            env_dicts["CUDA_MPS_ACTIVE_THREAD_PERCENTAGE"] = str(30)
        else:
            remove_environment_vatiables(["CUDA_MPS_ACTIVE_THREAD_PERCENTAGE"])
        update_environment_variables(env_dicts)
        p.start()
    [p.join(timeout=90) for p in processes]
