import torch
from torch import (
    distributed as dist,
    multiprocessing,
)
from vllm.config import VllmConfig
from vllm.distributed.kv_transfer.kv_lookup_buffer.simple_buffer import (
    SimpleKVLookupBuffer,
)
from vllm.distributed.kv_transfer.kv_pipe.torch_distributed_pipe import (
    TorchDistributedPipe,
)
from vllm.engine.arg_utils import EngineArgs
from vllm.utils import get_distributed_init_method, get_ip, get_open_port
from adrenaline.utils.profile_utils import profile


def drop_select_fn(
    rank,
    buffer: SimpleKVLookupBuffer,
    device: str,
    config: VllmConfig,
    seq_len: int = 512,
):
    model_config = config.model_config
    parallel_config = config.parallel_config

    tokens = torch.ones(seq_len, device=device)
    roi = torch.ones_like(tokens, dtype=torch.bool, device=device)
    hidden_size = model_config.get_hidden_size()
    head_size = model_config.get_head_size()
    kv_heads = model_config.get_num_kv_heads(parallel_config)
    dtype = model_config.dtype
    num_layers = model_config.get_num_layers(parallel_config)

    key = torch.rand([num_layers, seq_len, kv_heads, head_size], dtype=dtype, device=device)
    value = torch.rand([num_layers, seq_len, kv_heads, head_size], dtype=dtype, device=device)
    hidden_states = torch.rand([seq_len * hidden_size], dtype=dtype)

    if rank == 0:
        assert buffer.buffer_size == 0
        assert len(buffer.buffer) == 0

        buffer.insert(tokens, roi, key, value, hidden_states)

    torch.distributed.barrier()

    if rank == 1:
        tokens1, roi1, key1, value1, hidden_states1 = buffer.drop_select(tokens, roi)
        key_size = (key1.numel() * key1.element_size())
        value_size = (value1.numel() * value1.element_size())

    torch.distributed.barrier()
    if rank == 0:
        assert buffer.buffer_size == 0
        assert len(buffer.buffer) == 0


def process_fn(rank: int, dist_init_method: str):
    dist.init_process_group("gloo", dist_init_method, rank=rank, world_size=2)
    pipe = TorchDistributedPipe([[0, 1]], rank, dist.Backend.NCCL)
    cpu_pipe = TorchDistributedPipe([[0, 1]], rank, dist.Backend.GLOO)
    buffer = SimpleKVLookupBuffer(cpu_pipe, pipe, 170000000)

    engine_args = EngineArgs("meta-llama/Llama-2-7b-hf")
    config = engine_args.create_engine_config()
    with profile():
        for _ in range(2):
            drop_select_fn(rank, buffer, pipe.device, config)

    buffer.close()
    pipe.close()
    cpu_pipe.close()

    dist.destroy_process_group()


if __name__ == "__main__":
    ctx = multiprocessing.get_context("spawn")
    dist_init_method = get_distributed_init_method(get_ip(), get_open_port())
    world_size = 2
    processes = [
        ctx.Process(
            target=process_fn, args=(rank, dist_init_method)
        )
        for rank in range(world_size)
    ]
    [p.start() for p in processes]
    [p.join() for p in processes]
