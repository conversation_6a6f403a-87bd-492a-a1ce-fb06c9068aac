import argparse
import os
import time
import torch

from torch import multiprocessing
from torch import distributed as dist
from typing import Union, Optional
from vllm.attention.selector import backend_name_to_enum, global_force_attn_backend
from vllm.config import VllmConfig, LoadFormat
from vllm.distributed.kv_transfer import vllm_adapter as dist_kv
from vllm.distributed import parallel_state
from vllm.distributed.kv_transfer.agent.kv_transfer import KV_transfer_agent
from vllm.distributed.kv_transfer.agent.kv_transfer_in_device import KV_transfer_agent_in_same_device
from vllm.distributed.kv_transfer.agent.offload_exec_agent import Offload_attn_exec_agent
from vllm.engine.arg_utils import EngineArgs
from vllm.model_executor.utils import set_random_seed
from vllm.utils import (
    update_environment_variables,
    get_distributed_init_method,
    get_ip,
    get_open_port,
)
from vllm.worker.model_runner import ModelInputForGPUWithSamplingMetadata

from adrenaline.attention.backends import flashinfer
from adrenaline.model_runner.model_runner import <PERSON><PERSON><PERSON><PERSON>
from adrenaline.model_runner.attn_runner import Attention<PERSON>unner
from adrenaline.utils import input_factory
from adrenaline.utils.profile_utils import profile, nvtx_profiler

from vllm.logger import init_logger

logger = init_logger("vllm.benchmarks.offload_benchmarks.kv_transfer_agent")

def init_parallel_groups(rank: int, world_size: int, init_method: str):
    dist.init_process_group(
        backend=dist.Backend.GLOO,
        rank=rank,
        world_size=world_size,
        init_method=init_method,
    )
    parallel_state._PP = parallel_state.init_model_parallel_group(
        [[0], [1], [2]], 0, dist.Backend.NCCL, use_custom_allreduce=False, group_name="pp"
    )
    parallel_state._TP = parallel_state.init_model_parallel_group(
        [[0], [1], [2]], 0, dist.Backend.NCCL, use_message_queue_broadcaster=True, group_name="tp"
    )
    parallel_state._DISAGG = KV_transfer_agent([[[0, 1]]], 0)
    parallel_state._OFFLOAD_KV = KV_transfer_agent_in_same_device([[[0, 2]]], 0)
    parallel_state._OFFLOAD_EXEC = Offload_attn_exec_agent([[1, 2]], 0, decode_master_rank=1)


def destroy_parallel_groups():
    parallel_state.destroy_model_parallel()
    dist.destroy_process_group()


def query_recv_prefill_request(
    model_input: ModelInputForGPUWithSamplingMetadata,
):
    assert dist_kv.IS_KV_CONSUMER or dist_kv.IS_ATTN_EXECUTOR
    if dist_kv.IS_KV_CONSUMER:
        kv_transfer_agent = parallel_state.get_disagg_group()
    else:
        kv_transfer_agent = parallel_state.get_offload_kv_group()
    for idx, request_id in enumerate(model_input.request_ids):
        if kv_transfer_agent.query_recv_finished(request_id):
            model_input.is_recv_fins[idx] = True
    all_recv_fin = all(model_input.is_recv_fins)
    return all_recv_fin


iteration_cnt = 0

def execute_decode_request_with_kv_transfer(
    model_runner: Union[ModelRunner, AttentionRunner],
    prefill_model_input: ModelInputForGPUWithSamplingMetadata,
    decode_model_input: ModelInputForGPUWithSamplingMetadata,
    kv_caches: list[torch.Tensor],
):
    global iteration_cnt
    assert dist_kv.IS_KV_CONSUMER or dist_kv.IS_ATTN_EXECUTOR
    all_recv_fin = all(prefill_model_input.is_recv_fins)
    assert not all_recv_fin
    sche_group = parallel_state.get_offload_group().sche_comm.device_group
    recv_objects = [None]
    other_all_recv: bool = False
    while not (all_recv_fin and other_all_recv):
        logger.info(f"[rank{torch.distributed.get_rank()}] {prefill_model_input.is_recv_fins=}, {other_all_recv=}, {iteration_cnt=}")
        model_runner.execute_model(decode_model_input, kv_caches)
        iteration_cnt += 1
        all_recv_fin = query_recv_prefill_request(prefill_model_input)
        if dist_kv.IS_KV_CONSUMER:
            torch.distributed.send_object_list([all_recv_fin], dst=2, group=sche_group)
            torch.distributed.recv_object_list(recv_objects, src=2, group=sche_group)
            other_all_recv: bool = recv_objects[0]
        else:
            torch.distributed.recv_object_list(recv_objects, src=1, group=sche_group)
            torch.distributed.send_object_list([all_recv_fin], dst=1, group=sche_group)
            other_all_recv: bool = recv_objects[0]
    logger.info(f"[rank{torch.distributed.get_rank()}] {prefill_model_input.is_recv_fins=}, {other_all_recv=}, {iteration_cnt=}")
    model_runner.execute_model(prefill_model_input, kv_caches)
    iteration_cnt += 1


def sync_kv_transfer(
    model_runner: ModelRunner,
    model_input: ModelInputForGPUWithSamplingMetadata,
    kv_caches: list[torch.Tensor],
    sleep_time: float = 0,
):
    assert dist_kv.IS_KV_CONSUMER or dist_kv.IS_ATTN_EXECUTOR
    all_recv_fin = query_recv_prefill_request(model_input)
    while not all_recv_fin:
        time.sleep(sleep_time)
        all_recv_fin = query_recv_prefill_request(model_input)
    model_runner.execute_model(model_input, kv_caches)
    global iteration_cnt
    iteration_cnt += 1


def reset_model_input_recv_state(model_input: ModelInputForGPUWithSamplingMetadata):
    assert dist_kv.IS_KV_CONSUMER or dist_kv.IS_ATTN_EXECUTOR
    for idx in range(len(model_input.request_ids)):
        model_input.is_recv_fins[idx] = False


def transfer_kv_caches(
    model_runner: Union[ModelRunner, AttentionRunner],
    prefill_model_input: ModelInputForGPUWithSamplingMetadata,
    kv_caches: list[torch.Tensor],
    decode_model_input: Optional[ModelInputForGPUWithSamplingMetadata] = None,
):
    global iteration_cnt
    if dist_kv.IS_KV_PRODUCER:
        model_runner.execute_model(prefill_model_input, kv_caches)
        iteration_cnt += 1
    # wait for prefill instance prepare kv_caches
    dist.barrier()
    if dist_kv.IS_KV_CONSUMER or dist_kv.IS_ATTN_EXECUTOR:
        model_runner.execute_model(prefill_model_input, kv_caches)
        iteration_cnt += 1
        if decode_model_input is None:
            sync_kv_transfer(model_runner, prefill_model_input, kv_caches)
        else:
            execute_decode_request_with_kv_transfer(
                model_runner, prefill_model_input, decode_model_input, kv_caches
            )
        reset_model_input_recv_state(prefill_model_input)
    dist.barrier()


def worker_task(
    config: VllmConfig,
    input_len: int = 1024,
    prefill_batch: int = 3,
    decode_batch: int = 6,
    num_decode_offloads: int = 2,
):
    attn_backend_name = flashinfer.AdrenalineFlashInferBackend.get_name()
    global_force_attn_backend(backend_name_to_enum(attn_backend_name))

    if dist_kv.IS_KV_PRODUCER:
        model_runner = ModelRunner(vllm_config=config, is_driver_worker=True)
    elif dist_kv.IS_KV_CONSUMER:
        model_runner = ModelRunner(vllm_config=config, is_driver_worker=True)
    else:
        assert dist_kv.IS_ATTN_EXECUTOR
        model_runner = AttentionRunner(vllm_config=config, is_driver_worker=True)
    model_runner.load_model()

    offload_mask = [True] * prefill_batch
    prefill_model_input, num_prefill_blocks = (
        input_factory.prepare_prefill_model_input_and_num_blocks(
            config,
            model_runner,
            prefill_batch,
            input_len=input_len,
            offload_mask=offload_mask,
        )
    )
    if dist_kv.IS_KV_PRODUCER:
        decode_model_input, num_decode_blocks = None, None
    else:
        decode_model_input, num_decode_blocks = (
            input_factory.prepare_decode_model_runner_input_and_num_blocks(
                config,
                model_runner,
                decode_batch,
                input_len=input_len,
                num_offloads=num_decode_offloads,
            )
        )
    if num_decode_blocks is None:
        num_decode_blocks = 0

    block_size = config.cache_config.block_size
    min_blocks = config.model_config.max_model_len // block_size
    total_request_blocks = num_prefill_blocks + num_decode_blocks
    num_gpu_blocks = max(total_request_blocks, min_blocks)
    config.cache_config.num_gpu_blocks = num_gpu_blocks

    kv_caches = input_factory.build_kv_caches(config, num_gpu_blocks, "FLASHINFER")
    if not config.model_config.enforce_eager:
        kv_caches_for_capture = [kv_caches]
        model_runner.capture_model(kv_caches_for_capture)

    nvtx_profiler.enable()
    num_warmups, num_execs = 3, 3
    for _ in range(num_warmups):
        transfer_kv_caches(
            model_runner, prefill_model_input, kv_caches, decode_model_input
        )
    dist.barrier()
    with profile(enable_nvtx=False):
        dist.barrier()
        for _ in range(num_execs):
            transfer_kv_caches(
                model_runner, prefill_model_input, kv_caches, decode_model_input
            )
        dist.barrier()
    nvtx_profiler.disable()


def worker_fn(
    rank: int,
    world_size: int,
    dist_init_method: str,
    config: VllmConfig,
    input_len: int = 1024,
    prefill_batch: int = 3,
    decode_batch: int = 6,
    num_decode_offloads: int = 2,
):
    init_parallel_groups(rank, world_size, dist_init_method)
    worker_task(config, input_len, prefill_batch, decode_batch, num_decode_offloads)
    # destroy_parallel_groups()


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--input-len", type=int, default=512)
    parser.add_argument("--prefill-batch", type=int, default=3)
    parser.add_argument("--decode-batch", type=int, default=6)
    parser.add_argument("--num-prefill-offloads", type=int, default=2)
    parser.add_argument("--num-decode-offloads", type=int, default=2)
    parser.add_argument("--enforce-eager", action="store_true")
    args = parser.parse_args()
    return args

if __name__ == "__main__":
    args = parse_args()
    engine_args = EngineArgs(
        "meta-llama/Llama-2-7b-hf",
        load_format=LoadFormat.DUMMY,
        enforce_eager=args.enforce_eager,
    )
    config = engine_args.create_engine_config()
    ctx = multiprocessing.get_context("spawn")
    roles = ["producer", "consumer", "offload"]
    world_size = len(roles)
    devices = os.environ.get("CUDA_VISIBLE_DEVICES", "0,1,0").split(",")
    assert len(devices) == world_size
    dist_init_method = get_distributed_init_method(get_ip(), get_open_port())
    processes_args = [
        (
            rank,
            world_size,
            dist_init_method,
            config,
            args.input_len,
            args.prefill_batch,
            args.decode_batch,
            args.num_decode_offloads,
        )
        for rank in range(world_size)
    ]
    processes = [ctx.Process(target=worker_fn, args=args) for args in processes_args]
    assert "CUDA_MPS_ENABLE_PER_CTX_DEVICE_MULTIPROCESSOR_PARTITIONING" in os.environ.keys()
    for p, role, device in zip(processes, roles, devices):
        env_dicts = {
            "VLLM_ATTENTION_BACKEND": "ADRENALINE_FLASHINFER",
            "VLLM_DISTRIBUTED_KV_ROLE": role,
            "CUDA_VISIBLE_DEVICES": device,
            "VLLM_LOGGING_LEVEL": "DEBUG",
            "VLLM_ENABLE_KV_OFFLOAD": "1",
        }
        if role == "producer":
            env_dicts["CUDA_MPS_ACTIVE_THREAD_PERCENTAGE"] = str(70)
        elif role == "offload":
            env_dicts["CUDA_MPS_ACTIVE_THREAD_PERCENTAGE"] = str(30)
        else:
            env_dicts["CUDA_MPS_ACTIVE_THREAD_PERCENTAGE"] = str(100)
        update_environment_variables(env_dicts)
        p.start()
    [p.join(timeout=90) for p in processes]
