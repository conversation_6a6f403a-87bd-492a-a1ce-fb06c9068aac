import time
import torch
from torch import multiprocessing, distributed as dist

from vllm.distributed.kv_transfer.kv_pipe.py_pipe import PyPipe
from vllm.utils import get_distributed_init_method, get_ip, get_open_port

from adrenaline.utils.profile_utils import profile

def overlap_pypipe(rank: int, pipe: PyPipe, stream: torch.cuda.Stream):
    if rank == 0:
        x = torch.tensor([1.0, 2.0, 3.0]).to(pipe.device)
        t = torch.ones([256, 128, 128, 128]).to(pipe.device)
        for _ in range(3):
            t = (
                torch.log1p(t) * torch.sin(t)
                + torch.exp(torch.cos(t))
                - torch.sqrt(t + 1)
            )
        event = torch.cuda.Event()
        event.record(torch.cuda.current_stream())

        with torch.cuda.stream(stream):
            pipe.send_tensor(x)
            pipe.send_tensor(x)

        while not event.query():
            time.sleep(0)
        else:
            print("finished computation")
    else:
        x = torch.empty([3], dtype=torch.float32).to(pipe.device)
        with torch.cuda.stream(stream):
            x = pipe.recv_tensor()
            x = pipe.recv_tensor()

    dist.barrier()


def worker_fn(rank: int, world_size: int, dist_init_method: str):
    dist.init_process_group(
        backend=dist.Backend.GLOO, init_method=dist_init_method, rank=rank, world_size=world_size
    )
    group_ranks = [list(range(world_size))]
    pipe = PyPipe(group_ranks, rank, "nccl")
    stream = torch.cuda.Stream(rank)
    # warm up
    overlap_pypipe(rank, pipe, stream)
    with profile():
        overlap_pypipe(rank, pipe, stream)
    dist.destroy_process_group()


if __name__ == "__main__":
    ctx = multiprocessing.get_context("spawn")
    world_size = 2
    dist_init_method = get_distributed_init_method(get_ip(), get_open_port())
    processes = [
        ctx.Process(target=worker_fn, args=(rank, world_size, dist_init_method))
        for rank in range(world_size)
    ]
    [p.start() for p in processes]
    [p.join() for p in processes]
