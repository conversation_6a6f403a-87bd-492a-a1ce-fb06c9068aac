import argparse
import os
import torch

from torch import multiprocessing
from torch import distributed as dist
from typing import Union, Optional
from vllm.attention.selector import backend_name_to_enum, global_force_attn_backend
from vllm.config import VllmConfig, LoadFormat
from vllm.distributed.kv_transfer import vllm_adapter as dist_kv
from vllm.distributed import parallel_state
from vllm.distributed.kv_transfer.agent.offload_exec_agent import Offload_attn_exec_agent
from vllm.engine.arg_utils import EngineArgs
from vllm.model_executor.utils import set_random_seed
from vllm.utils import (
    update_environment_variables,
    get_distributed_init_method,
    get_ip,
    get_open_port,
)
from vllm.worker.model_runner import ModelInputForGPUWithSamplingMetadata
from vllm.utils import cdiv

from adrenaline.attention.backends import flashinfer
from adrenaline.model_runner.attn_runner import AttentionRunner
from adrenaline.model_runner.model_runner import ModelRunner
from adrenaline.utils import input_factory
from adrenaline.utils.profile_utils import profile


def init_parallel_groups(rank: int, world_size: int, init_method: str):
    dist.init_process_group(
        backend=dist.Backend.GLOO,
        rank=rank,
        world_size=world_size,
        init_method=init_method,
    )
    parallel_state._PP = parallel_state.init_model_parallel_group(
        [[0], [1]], 0, dist.Backend.NCCL, use_custom_allreduce=False, group_name="pp"
    )
    parallel_state._TP = parallel_state.init_model_parallel_group(
        [[0], [1]], 0, dist.Backend.NCCL, use_message_queue_broadcaster=True, group_name="tp"
    )
    parallel_state._OFFLOAD_EXEC = Offload_attn_exec_agent([[0, 1]], 0, decode_master_rank=0)


def destroy_parallel_groups():
    parallel_state.destroy_model_parallel()
    dist.destroy_process_group()


def execute_model(
    model_runner: Union[ModelRunner, AttentionRunner],
    model_input: ModelInputForGPUWithSamplingMetadata,
    kv_caches: list[torch.Tensor],
):
    if dist_kv.IS_KV_PRODUCER:
        assert isinstance(model_runner, ModelRunner)
        return model_runner.execute_model(model_input, kv_caches)
    elif dist_kv.IS_KV_CONSUMER:
        assert isinstance(model_runner, ModelRunner)
        return model_runner.execute_model(model_input, kv_caches)
    else:
        assert dist_kv.IS_ATTN_EXECUTOR
        if model_input is None:
            return None
        assert isinstance(model_runner, AttentionRunner)
        assert kv_caches is not None
        return model_runner.execute_model(model_input, kv_caches)


def prepare_request_lens(
    num_locals: int,
    num_offloads: int,
    local_input_len: int,
    offload_input_len: int,
    max_output_len: int,
):
    local_reqs = [
        ("", local_input_len, max_output_len, None) for _ in range(num_locals)
    ]
    offload_reqs = [
        ("", offload_input_len, max_output_len, None) for _ in range(num_offloads)
    ]
    requests = local_reqs + offload_reqs
    return requests


def worker_task(
    config: VllmConfig,
    num_locals: int = 3,
    num_offloads: int = 0,
    local_input_len: Optional[int] = None,
    offload_input_len: Optional[int] = None,
    max_output_len: Optional[int] = None,
    num_warmups: int = 2,
    num_execs: int = 10,
    seed: int = 0,
):
    attn_backend_name = flashinfer.AdrenalineFlashInferBackend.get_name()
    global_force_attn_backend(backend_name_to_enum(attn_backend_name))
    block_size = config.cache_config.block_size
    set_random_seed(seed)
    requests = prepare_request_lens(
        num_locals, num_offloads, local_input_len, offload_input_len, max_output_len
    )
    input_lens = [req[1] for req in requests]
    max_output_lens = [req[2] for req in requests]
    if dist_kv.IS_KV_CONSUMER:
        model_runner = ModelRunner(vllm_config=config, is_driver_worker=True)
        total_seq_lens = sum([req[1] + req[2] + 1 for req in requests[:num_locals]])
        num_gpu_blocks = cdiv(total_seq_lens, block_size)
    else:
        assert dist_kv.IS_ATTN_EXECUTOR
        model_runner = AttentionRunner(vllm_config=config, is_driver_worker=True)
        total_seq_lens = sum([req[1] + req[2] + 1 for req in requests[num_locals:]])
        num_gpu_blocks = cdiv(total_seq_lens, block_size)

    model_runner.load_model()
    config.cache_config.num_gpu_blocks = num_gpu_blocks
    kv_caches = input_factory.build_kv_caches(config, num_gpu_blocks, "FLASHINFER")
    if not config.model_config.enforce_eager:
        kv_caches_for_capture = [kv_caches]
        model_runner.capture_model(kv_caches_for_capture)
    set_random_seed(seed)

    model_input = input_factory.prepare_decode_model_runner_input(
        config,
        model_runner,
        num_locals + num_offloads,
        input_lens,
        max_output_lens,
        num_offloads=num_offloads,
        num_gpu_blocks=num_gpu_blocks,
    )

    if model_input is not None:
        assert isinstance(model_input.attn_metadata, flashinfer.AdrenalineFlashInferMetadata)

    for _ in range(num_warmups):
        execute_model(model_runner, model_input, kv_caches)

    dist.barrier()
    with profile():
        dist.barrier()
        for _ in range(num_execs):
            execute_model(model_runner, model_input, kv_caches)
        dist.barrier()


def worker_fn(
    rank: int,
    world_size: int,
    dist_init_method: str,
    config: VllmConfig,
    num_locals: int = 3,
    num_offloads: int = 0,
    local_input_len: int = None,
    offload_input_len: int = None,
    max_output_len: Optional[int] = None,
    num_warmups: int = 2,
    num_execs: int = 2,
    seed: int = 0,
):
    init_parallel_groups(rank, world_size, dist_init_method)
    worker_task(
        config,
        num_locals,
        num_offloads,
        local_input_len,
        offload_input_len,
        max_output_len,
        num_warmups,
        num_execs,
        seed,
    )
    # destroy_parallel_groups()


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--num-locals", type=int)
    parser.add_argument("--num-offloads", type=int)
    parser.add_argument("--local-input-len", type=int)
    parser.add_argument("--offload-input-len", type=int)
    parser.add_argument("--max-output-len", type=int, default=16)
    parser.add_argument("--offload-ratio", type=float, default=0.6)
    parser.add_argument("--num-warmups", type=int, default=2)
    parser.add_argument("--num-execs", type=int, default=10)
    parser.add_argument("--enforce-eager", action="store_true")
    parser.add_argument("--seed", type=int, default=42)
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    print(args)
    enforce_eager = args.enforce_eager
    engine_args = EngineArgs(
        "meta-llama/Llama-2-7b-hf",
        load_format=LoadFormat.DUMMY,
        enforce_eager=enforce_eager,
    )
    config = engine_args.create_engine_config()
    ctx = multiprocessing.get_context("spawn")
    roles = ["consumer", "offload"]
    world_size = len(roles)
    devices = os.environ.get("CUDA_VISIBLE_DEVICES", "0,1").split(",")
    assert len(devices) == world_size
    dist_init_method = get_distributed_init_method(get_ip(), get_open_port())
    processes_args = [
        (
            rank,
            world_size,
            dist_init_method,
            config,
            args.num_locals,
            args.num_offloads,
            args.local_input_len,
            args.offload_input_len,
            args.max_output_len,
            args.num_warmups,
            args.num_execs,
            args.seed,
        )
        for rank in range(world_size)
    ]
    processes = [ctx.Process(target=worker_fn, args=args) for args in processes_args]
    assert (
        "CUDA_MPS_ENABLE_PER_CTX_DEVICE_MULTIPROCESSOR_PARTITIONING"
        in os.environ.keys()
    )
    for p, role, device in zip(processes, roles, devices):
        env_dicts = {
            "CUDA_VISIBLE_DEVICES": device,
            "VLLM_ATTENTION_BACKEND": "ADRENALINE_FLASHINFER",
            "VLLM_DISTRIBUTED_KV_ROLE": role,
            "VLLM_ENABLE_KV_OFFLOAD": "1",
            "VLLM_LOGGING_LEVEL": "DEBUG",
            "VLLM_OFFLOAD_RATIO": str(args.offload_ratio),
        }
        if role == "offload":
            env_dicts["CUDA_MPS_ACTIVE_THREAD_PERCENTAGE"] = str(30)
        else:
            env_dicts["CUDA_MPS_ACTIVE_THREAD_PERCENTAGE"] = str(100)
        update_environment_variables(env_dicts)
        p.start()
    [p.join(timeout=90) for p in processes]
