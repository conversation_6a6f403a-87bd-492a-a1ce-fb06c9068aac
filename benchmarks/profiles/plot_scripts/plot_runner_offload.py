import csv
import os

from matplotlib import pyplot
from adrenaline.utils.profile_utils import profile_dir


if __name__ == "__main__":
    total_batch = 70
    offload_pcts = [0.05 * i for i in range(7)]
    offload_times: dict[int, float] = {}
    with open(f"{profile_dir}/runner_offload.csv", "r") as f:
        reader = csv.DictReader(f)
        # non-offload
        row0 = next(reader)
        row1 = next(reader)
        local_time = max(
            float(row0["avg_forward_time"]), float(row1["avg_forward_time"])
        )
        # offload
        for offload_pct in offload_pcts:
            row0 = next(reader)
            row1 = next(reader)
            offload_time = max(
                float(row0["avg_forward_time"]), float(row1["avg_forward_time"])
            )
            offload_times[offload_pct] = offload_time

    print(offload_times)
    pyplot.axhline(local_time, linestyle="--", label="local")
    # offload : local
    offload_rates = [offload_pct for offload_pct in offload_pcts]
    pyplot.plot(offload_rates, offload_times.values(), marker="o", label="offload")
    pyplot.xlabel("offload_batch / total_batch")
    pyplot.ylabel("forward time (ms)")
    pyplot.legend()

    figure_dir = f"{profile_dir}/figures"
    os.makedirs(figure_dir, exist_ok=True)
    pyplot.savefig(f"{figure_dir}/runner_offload.jpg")
