import csv
import os

from matplotlib import pyplot
from adrenaline.utils.profile_utils import profile_dir


if __name__ == "__main__":
    offload_batches = [5, 10, 15, 20, 25, 30]
    local_batch = 73
    offload_times: dict[int, float] = {}
    with open(f"{profile_dir}/runner_overflow_offload.csv", "r") as f:
        reader = csv.DictReader(f)
        # non-offload
        row0 = next(reader)
        row1 = next(reader)
        local_time = max(
            float(row0["avg_forward_time"]), float(row1["avg_forward_time"])
        )
        # offload
        for offload_batch in offload_batches:
            row0 = next(reader)
            row1 = next(reader)
            offload_time = max(
                float(row0["avg_forward_time"]), float(row1["avg_forward_time"])
            )
            offload_times[offload_batch] = offload_time

    print(offload_times)
    pyplot.axhline(local_time, linestyle="--", label="no-offload")
    # offload : local
    offload_rates = [offload_batch / local_batch for offload_batch in offload_batches]
    pyplot.plot(offload_rates, offload_times.values(), marker="o", label="local+offload")
    for offload_rate, offload_time in zip(offload_rates, offload_times.values()):
        pyplot.text(offload_rate, offload_time * 0.99, f"{offload_time:.2f}", ha="center")
    pyplot.xlabel("offload_batch : local_batch")
    pyplot.ylabel("forward time (ms)")
    pyplot.legend()
    pyplot.tight_layout()

    figure_dir = f"{profile_dir}/figures"
    os.makedirs(figure_dir, exist_ok=True)
    pyplot.savefig(f"{figure_dir}/runner_overflow_offload.jpg")
