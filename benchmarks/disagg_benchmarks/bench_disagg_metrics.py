import argparse
import os
import torch

from torch import multiprocessing
from torch import distributed as dist
from vllm.attention.selector import backend_name_to_enum, global_force_attn_backend
from vllm.config import VllmConfig, LoadFormat
from vllm.distributed import parallel_state
from vllm.distributed.utils import TransferUtils
from vllm.distributed.kv_transfer import vllm_adapter as dist_kv
from vllm.distributed.kv_transfer.agent.kv_transfer import KV_transfer_agent
from vllm.distributed.kv_transfer.kv_lookup_buffer.simple_buffer import SimpleKVLookupBuffer
from vllm.engine.arg_utils import EngineArgs
from vllm.model_executor.utils import set_random_seed
from vllm.worker.model_runner import ModelRunner, ModelInputForGPUWithSamplingMetadata
from vllm.utils import (
    update_environment_variables,
    get_distributed_init_method,
    get_ip,
    get_open_port,
    cdiv,
)

from adrenaline.attention.backends.flashinfer import AdrenalineFlashInferBackend
from adrenaline.utils import input_factory
from adrenaline.utils.profile_utils import profile


def init_parallel_groups(rank: int, world_size: int, init_method: str, transfer_utils: TransferUtils):
    dist.init_process_group(
        backend=dist.Backend.GLOO,
        rank=rank,
        world_size=world_size,
        init_method=init_method,
    )
    parallel_state._PP = parallel_state.init_model_parallel_group(
        [[0], [1]], 0, dist.Backend.NCCL, use_custom_allreduce=False, group_name="pp"
    )
    parallel_state._TP = parallel_state.init_model_parallel_group(
        [[0], [1]], 0, dist.Backend.NCCL, use_message_queue_broadcaster=True, group_name="tp"
    )
    parallel_state._DISAGG = KV_transfer_agent([[[0, 1]]], 0, transfer_utils=transfer_utils)


def destroy_parallel_groups():
    parallel_state.destroy_model_parallel()
    dist.destroy_process_group()


def execute_model(
    model_runner: ModelRunner,
    model_input: ModelInputForGPUWithSamplingMetadata,
    kv_caches: list[torch.Tensor],
):
    if dist_kv.IS_KV_PRODUCER:
        return model_runner.execute_model(model_input, kv_caches)
    else:
        assert dist_kv.IS_KV_CONSUMER
        return model_runner.execute_model(model_input, kv_caches)


def clear_prefill_send_buffer():
    if dist_kv.IS_KV_PRODUCER:
        send_buf: SimpleKVLookupBuffer = parallel_state._DISAGG.send_buffer
        send_buf.clear()
        assert send_buf.buffer_size == 0


def worker_task(
    config: VllmConfig,
    input_len: int = 1024,
    max_output_len: int = 128,
    decode_batch: int = 3,
    num_prefills: int = 1,
):
    global_force_attn_backend(backend_name_to_enum(AdrenalineFlashInferBackend.get_name()))

    model_runner = ModelRunner(vllm_config=config, is_driver_worker=True)
    model_runner.load_model()

    max_len_per_request = input_len + max_output_len + 1
    block_size = config.cache_config.block_size
    if dist_kv.IS_KV_PRODUCER:
        num_gpu_blocks = cdiv(max_len_per_request, block_size) * num_prefills
    else:
        assert dist_kv.IS_KV_CONSUMER
        num_gpu_blocks = cdiv(max_len_per_request, block_size) * decode_batch
    config.cache_config.num_gpu_blocks = num_gpu_blocks
    kv_caches = input_factory.build_kv_caches(config, num_gpu_blocks, "FLASHINFER")
    if not config.model_config.enforce_eager and not dist_kv.IS_KV_PRODUCER:
        kv_caches_for_capture = [kv_caches]
        model_runner.capture_model(kv_caches_for_capture)

    set_random_seed(0)

    if dist_kv.IS_KV_PRODUCER:
        model_input = input_factory.prepare_prefill_model_input(
            config, model_runner, input_len=input_len, num_gpu_blocks=num_gpu_blocks
        )
    else:
        model_input = input_factory.prepare_decode_model_runner_input(
            config,
            model_runner,
            decode_batch,
            input_len=input_len,
            max_output_len=max_output_len,
            num_gpu_blocks=num_gpu_blocks,
        )

    num_warmups, num_execs = 2, 3
    for _ in range(num_warmups):
        execute_model(model_runner, model_input, kv_caches)
        clear_prefill_send_buffer()
    torch.cuda.synchronize()
    dist.barrier()

    with profile():
        dist.barrier()
        for _ in range(num_execs):
            execute_model(model_runner, model_input, kv_caches)
            clear_prefill_send_buffer()
        dist.barrier()


def worker_fn(
    rank: int,
    world_size: int,
    dist_init_method: str,
    config: VllmConfig,
    input_len: int = 1024,
    max_output_len: int = 128,
    decode_batch: int = 3,
):
    transfer_utils = TransferUtils(
        hidden_size=config.model_config.get_hidden_size(),
        num_kv_heads=config.model_config.get_num_kv_heads(config.parallel_config),
        num_layers=config.model_config.get_num_layers(config.parallel_config),
        head_size=config.model_config.get_head_size(),
        max_model_len=config.model_config.max_model_len,
        kv_dtype=config.model_config.dtype,
    )
    init_parallel_groups(rank, world_size, dist_init_method, transfer_utils)
    worker_task(config, input_len, max_output_len, decode_batch)
    # destroy_parallel_groups()


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--input-len", type=int, default=1024)
    parser.add_argument("--output-len", type=int, default=128)
    parser.add_argument("--decode-batch", type=int, default=3)
    parser.add_argument("--model", type=str, default="meta-llama/Llama-2-7b-hf")
    parser.add_argument("--enforce-eager", action="store_true")
    args = parser.parse_args()
    return args

if __name__ == "__main__":
    args = parse_args()
    print(args)
    engine_args = EngineArgs(
        args.model,
        enforce_eager=args.enforce_eager,
        load_format=LoadFormat.DUMMY,
    )
    config = engine_args.create_engine_config()
    ctx = multiprocessing.get_context("spawn")
    roles = ["producer", "consumer"]
    devices = os.environ.get("CUDA_VISIBLE_DEVICES", "0,1").split(",")
    world_size = len(roles)
    dist_init_method = get_distributed_init_method(get_ip(), get_open_port())
    processes_args = [
        (
            rank,
            world_size,
            dist_init_method,
            config,
            args.input_len,
            args.output_len,
            args.decode_batch,
        )
        for rank in range(world_size)
    ]
    processes = [ctx.Process(target=worker_fn, args=args) for args in processes_args]
    for p, role, device in zip(processes, roles, devices):
        update_environment_variables({
            "VLLM_ATTENTION_BACKEND": "ADRENALINE_FLASHINFER",
            "VLLM_DISTRIBUTED_KV_ROLE": role,
            "CUDA_VISIBLE_DEVICES": device,
            "VLLM_LOGGING_LEVEL": "DEBUG",
        })
        p.start()
    [p.join() for p in processes]
