import argparse
import os
import time
import torch

from torch import multiprocessing
from torch import distributed as dist
from typing import Union, Optional
from vllm.attention.selector import backend_name_to_enum, global_force_attn_backend
from vllm.config import VllmConfig, LoadFormat
from vllm.distributed.kv_transfer import vllm_adapter as dist_kv
from vllm.distributed import parallel_state
from vllm.distributed.kv_transfer.agent.kv_transfer import KV_transfer_agent
from vllm.engine.arg_utils import EngineArgs
from vllm.model_executor.utils import set_random_seed
from vllm.utils import (
    update_environment_variables,
    get_distributed_init_method,
    get_ip,
    get_open_port,
)
from vllm.worker.model_runner import (
    ModelInputForGPUWithSamplingMetadata,
    ModelRunner,
)

from adrenaline.attention.backends import flashinfer
from adrenaline.model_runner.attn_runner import AttentionRunner
from adrenaline.utils import input_factory
from adrenaline.utils.profile_utils import profile, nvtx_profiler


def init_parallel_groups(rank: int, world_size: int, init_method: str):
    dist.init_process_group(
        backend=dist.Backend.GLOO,
        rank=rank,
        world_size=world_size,
        init_method=init_method,
    )
    parallel_state._PP = parallel_state.init_model_parallel_group(
        [[0], [1]], 0, dist.Backend.NCCL, use_custom_allreduce=False, group_name="pp"
    )
    parallel_state._TP = parallel_state.init_model_parallel_group(
        [[0], [1]], 0, dist.Backend.NCCL, use_message_queue_broadcaster=True, group_name="tp"
    )
    parallel_state._DISAGG = KV_transfer_agent([[[0, 1]]], 0)


def destroy_parallel_groups():
    parallel_state.destroy_model_parallel()
    dist.destroy_process_group()


def query_recv_prefill_request(
    model_input: ModelInputForGPUWithSamplingMetadata,
):
    assert dist_kv.IS_KV_CONSUMER
    for idx, request_id in enumerate(model_input.request_ids):
        if parallel_state.get_disagg_group().query_recv_finished(request_id):
            model_input.is_recv_fins[idx] = True
    all_recv_fin = all(model_input.is_recv_fins)
    return all_recv_fin


def execute_decode_request_with_kv_transfer(
    model_runner: ModelRunner,
    prefill_model_input: ModelInputForGPUWithSamplingMetadata,
    decode_model_input: ModelInputForGPUWithSamplingMetadata,
    kv_caches: list[torch.Tensor],
):
    assert dist_kv.IS_KV_CONSUMER
    all_recv_fin = all(prefill_model_input.is_recv_fins)
    assert not all_recv_fin
    while not all_recv_fin:
        model_runner.execute_model(decode_model_input, kv_caches)
        all_recv_fin = query_recv_prefill_request(prefill_model_input)
    model_runner.execute_model(prefill_model_input, kv_caches)


def sync_kv_transfer(
    model_runner: ModelRunner,
    model_input: ModelInputForGPUWithSamplingMetadata,
    kv_caches: list[torch.Tensor],
    sleep_time: float = 0,
):
    assert dist_kv.IS_KV_CONSUMER
    all_recv_fin = query_recv_prefill_request(model_input)
    while not all_recv_fin:
        time.sleep(sleep_time)
        all_recv_fin = query_recv_prefill_request(model_input)
    model_runner.execute_model(model_input, kv_caches)


def reset_model_input_recv_state(model_input: ModelInputForGPUWithSamplingMetadata):
    assert dist_kv.IS_KV_CONSUMER
    for idx in range(len(model_input.request_ids)):
        model_input.is_recv_fins[idx] = False


def transfer_kv_caches(
    model_runner: ModelRunner,
    prefill_model_input: ModelInputForGPUWithSamplingMetadata,
    kv_caches: list[torch.Tensor],
    decode_model_input: Optional[ModelInputForGPUWithSamplingMetadata] = None,
):
    if dist_kv.IS_KV_PRODUCER:
        model_runner.execute_model(prefill_model_input, kv_caches)
    # wait for prefill instance prepare kv_caches
    dist.barrier()

    if dist_kv.IS_KV_CONSUMER:
        model_runner.execute_model(prefill_model_input, kv_caches)
        if decode_model_input is None:
            sync_kv_transfer(model_runner, prefill_model_input, kv_caches)
        else:
            execute_decode_request_with_kv_transfer(
                model_runner, prefill_model_input, decode_model_input, kv_caches
            )
        reset_model_input_recv_state(prefill_model_input)

    dist.barrier()


def worker_task(
    config: VllmConfig,
    input_len: int = 1024,
    prefill_batch: int = 3,
    decode_batch: int = 3,
):
    attn_backend_name = flashinfer.AdrenalineFlashInferBackend.get_name()
    global_force_attn_backend(backend_name_to_enum(attn_backend_name))
    model_runner = ModelRunner(vllm_config=config, is_driver_worker=True)
    model_runner.load_model()

    set_random_seed(0)
    prefill_model_input, num_prefill_blocks = (
        input_factory.prepare_prefill_model_input_and_num_blocks(
            config, model_runner, prefill_batch, input_len=input_len
        )
    )
    if dist_kv.IS_KV_PRODUCER:
        decode_model_input, num_decode_blocks = None, None
    else:
        decode_model_input, num_decode_blocks = (
            input_factory.prepare_decode_model_runner_input_and_num_blocks(
                config,
                model_runner,
                decode_batch,
                input_len=input_len,
                block_offset=num_prefill_blocks,
            )
        )
    if num_decode_blocks is None:
        num_decode_blocks = 0
    total_request_blocks = num_prefill_blocks + num_decode_blocks
    block_size = config.cache_config.block_size
    min_blocks = config.model_config.max_model_len // block_size
    total_blocks = max(total_request_blocks, min_blocks)
    config.cache_config.num_gpu_blocks = total_blocks

    kv_caches = input_factory.build_kv_caches(config, total_blocks, "FLASHINFER")
    if not config.model_config.enforce_eager:
        kv_caches_for_capture = [kv_caches]
        model_runner.capture_model(kv_caches_for_capture)

    nvtx_profiler.enable()
    num_warmups, num_execs = 2, 3
    for _ in range(num_warmups):
        transfer_kv_caches(
            model_runner, prefill_model_input, kv_caches, decode_model_input
        )
    with profile(enable_nvtx=False):
        for _ in range(num_execs):
            transfer_kv_caches(
                model_runner, prefill_model_input, kv_caches, decode_model_input
            )
    nvtx_profiler.disable()


def worker_fn(
    rank: int,
    world_size: int,
    dist_init_method: str,
    config: VllmConfig,
    input_len: int = 1024,
    prefill_batch: int = 3,
    decode_batch: int = 3,
):
    init_parallel_groups(rank, world_size, dist_init_method)
    worker_task(config, input_len, prefill_batch, decode_batch)
    # destroy_parallel_groups()


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--input-len", type=int, default=1024)
    parser.add_argument("--prefill-batch", type=int, default=3)
    parser.add_argument("--decode-batch", type=int, default=3)
    parser.add_argument("--enforce-eager", action="store_true")
    args = parser.parse_args()
    return args


if __name__ == "__main__":
    args = parse_args()
    engine_args = EngineArgs(
        "meta-llama/Llama-2-7b-hf",
        load_format=LoadFormat.DUMMY,
        enforce_eager=args.enforce_eager,
    )
    config = engine_args.create_engine_config()
    ctx = multiprocessing.get_context("spawn")
    roles = ["producer", "consumer"]
    world_size = len(roles)
    devices = os.environ.get("CUDA_VISIBLE_DEVICES", "0,1").split(",")
    assert len(devices) == world_size
    dist_init_method = get_distributed_init_method(get_ip(), get_open_port())
    processes_args = [
        (
            rank,
            world_size,
            dist_init_method,
            config,
            args.input_len,
            args.prefill_batch,
            args.decode_batch,
        )
        for rank in range(world_size)
    ]
    processes = [ctx.Process(target=worker_fn, args=args) for args in processes_args]
    assert "CUDA_MPS_ENABLE_PER_CTX_DEVICE_MULTIPROCESSOR_PARTITIONING" in os.environ.keys()
    for p, role, device in zip(processes, roles, devices):
        env_dicts = {
            "VLLM_ATTENTION_BACKEND": "ADRENALINE_FLASHINFER",
            "VLLM_DISTRIBUTED_KV_ROLE": role,
            "CUDA_VISIBLE_DEVICES": device,
            "VLLM_LOGGING_LEVEL": "DEBUG",
        }
        update_environment_variables(env_dicts)
        p.start()
    [p.join(timeout=60) for p in processes]
